name: tencent_cloud_chat_uikit
description: A powerful chat UI component library and business logic for Tencent Cloud Chat, creating seamless in-app chat modules for delightful user experiences.
version: 3.1.0+1
homepage: https://trtc.io/products/chat?utm_source=gfs&utm_medium=link&utm_campaign=%E6%B8%A0%E9%81%93&_channel_track_key=k6WgfCKn
repository: https://github.com/TencentCloud/chat-uikit-flutter
documentation: https://comm.qq.com/im/doc/flutter/en/TUIKit/readme.html
publish_to: none
platforms:
  android:
  ios:
  macos:
  web:
  windows:

environment:
  sdk: ">=3.0.0 <4.0.0"
  flutter: ">=3.24.0"

dependencies:
  flutter:
    sdk: flutter
  adaptive_action_sheet: ^2.0.1
  provider: ^6.0.1
  intl: ^0.19.0
  get_it: ^7.2.0
  dotted_border: ^2.0.0+2
  flutter_svg: ^2.0.6
  flutter_audio_waveforms: ^1.2.1+8
  flutter_lucide: ^1.5.0
  image_picker: ^1.1.2
  file_picker: 8.1.4
  tencent_super_tooltip:
    path: ../tencent_super_tooltip-0.0.1
  video_player: ^2.9.0
  chewie: ^1.8.5
  flutter_slidable:
    git:
      url: https://github.com/letsar/flutter_slidable.git
  path_provider: ^2.0.8
  cached_network_image: ^3.3.0
  shared_preferences: ^2.0.13
  scroll_to_index: ^2.1.1
  wechat_assets_picker: ^9.4.1
  wechat_camera_picker: ^4.3.6
  flutter_easyrefresh: ^2.2.1
  extended_image: ^9.0.0
  extended_text_field: ^16.0.0
  extended_text: ^15.0.0
  flutter_bounceable: ^1.1.0
  package_info_plus: ^8.0.0
  loading_animation_widget: ^1.1.0+3
  permission_handler: 11.3.1
  tuple: ^2.0.0
  flutter_markdown: ^0.6.15
  url_launcher: ^6.1.4
  lottie: ^3.3.0
  universal_html: ^2.2.2
  link_preview_generator_for_us: ^2.0.0
  http: ^1.0.0
  crypto: ^3.0.2
  collection: ^1.15.0
  flutter_image_compress: ^2.3.0
  uuid: ^4.5.1
  open_file: ^3.3.2
  tencent_keyboard_visibility: ^1.0.1
  tim_ui_kit_sticker_plugin: 
    path: ../tim_ui_kit_sticker_plugin-3.2.0
  tencent_im_base: ^8.0.0
  fc_native_video_thumbnail: ^0.16.0
  path: ^1.8.1
  tencent_cloud_uikit_core: ^1.6.0
  pasteboard: ^0.2.0
  desktop_drop: ^0.4.4
  device_info_plus: ^10.1.2
  cross_file: ^0.3.3+4
  csslib: ^0.17.2
  diff_match_patch: ^0.4.1
  just_audio: ^0.9.34
  markdown: ^7.1.0
  logger: ^2.0.1
  visibility_detector: ^0.4.0+2
  azlistview: ^2.0.0
  lpinyin: ^2.0.3
  image_gallery_saver_plus: ^4.0.0
  transparent_image: ^2.0.1
  record: 5.1.2
  webview_flutter: ^4.8.0
  pointer_interceptor: ^0.10.1+2

  tencent_cloud_chat_sdk: 8.3.6498+1
  # flutter_plugin_record_plus_plus: ^1.0.3
  # flutter_plugin_record_plus_plus:
  #   git:
  #     url: https://github.com/Uvais-Mohammad/flutter_plugin_record_plus_plus.git
dev_dependencies:
  flutter_lints: ^1.0.0
  build_runner: any
  lints: ^1.0.1

dependency_overrides:
  record_android: 1.2.0
  # photo_manager: ^3.6.4
#   tencent_chat_i18n_tool:
#     path: ../../../tencent_chat_i18n_tool

#  tencent_cloud_chat_sdk:
#    path: ../../../tencent_cloud_chat_sdk

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  uses-material-design: true
  assets:
    - images/
    - images/svg/
    # - assets/custom_face_resource/4349/

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_chat_model_tools.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_chat_global_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/message/message_services.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/recent_conversation_list.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/share_card.dart';

class TIMUIKitShare extends StatefulWidget {
  const TIMUIKitShare({
    super.key,
    required this.data,
    required this.onSend,
    required this.borderColor,
    required this.backgroundColor,
    
  });

  final Map<String, dynamic> data;
  final Function(V2TimValueCallback<V2TimMessage>?) onSend;
  final Color? borderColor;
  final Color? backgroundColor;
  @override
  State<TIMUIKitShare> createState() => _TIMUIKitShareState();
}

class _TIMUIKitShareState extends State<TIMUIKitShare> {
  final MessageService _messageService = serviceLocator<MessageService>();
  final TUIChatModelTools tools = serviceLocator<TUIChatModelTools>();
  void shareMessage() async {}

  Future<V2TimValueCallback<V2TimMessage>?> sendCustomMessage({
    required String data,
    required String convID,
    required ConvType convType,
  }) async {
    final customMessageInfo = await _messageService.createCustomMessage(data: data);
    final messageInfo = customMessageInfo!.messageInfo;
    if (messageInfo != null) {
      final messageInfoWithSender = tools.setUserInfoForMessage(messageInfo, customMessageInfo.id!);
      messageInfoWithSender.status = MessageStatus.V2TIM_MSG_STATUS_SENDING;
      return _sendMessage(
          convID: convID,
          id: customMessageInfo.id as String,
          convType: convType,
          offlinePushInfo: tools.buildMessagePushInfo(customMessageInfo.messageInfo!, convID, convType));
    }
    return null;
  }

  void showDialogBox({
    required String data,
    required String convId,
    required ConvType convType,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return CMProduct(
          data: jsonDecode(data),
          actionWidget: Row(
            spacing: 10,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              OutlinedButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                child: const Text('Cancel'),
              ),
              FilledButton(
                style: FilledButton.styleFrom(
                  backgroundColor: widget.backgroundColor,
                ),
                onPressed: () async {
                  if (convType == ConvType.group) {
                    final convID = convId.replaceAll('group_', '');
                    widget.onSend(await sendCustomMessage(
                        data: jsonEncode(widget.data), convID: convID, convType: ConvType.group));
                  } else {
                    final convID = convId.replaceAll('c2c_', '');
                    widget.onSend(
                        await sendCustomMessage(data: jsonEncode(widget.data), convID: convID, convType: ConvType.c2c));
                  }
                  Navigator.pop(context);
                },
                child: const Text(' Confirm '),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<V2TimValueCallback<V2TimMessage>> _sendMessage({
    required String id,
    required String convID,
    required ConvType convType,
    OfflinePushInfo? offlinePushInfo,
    bool? onlineUserOnly = false,
    MessagePriorityEnum priority = MessagePriorityEnum.V2TIM_PRIORITY_NORMAL,
    bool? isExcludedFromUnreadCount,
    String? cloudCustomData,
    String? localCustomData,
  }) async {
    String receiver = convType == ConvType.c2c ? convID : '';
    String groupID = convType == ConvType.group ? convID : '';
    final sendMsgRes = await _messageService.sendMessage(
        priority: priority,
        localCustomData: localCustomData,
        isExcludedFromUnreadCount: isExcludedFromUnreadCount ?? false,
        id: id,
        receiver: receiver,
        groupID: groupID,
        offlinePushInfo: offlinePushInfo,
        onlineUserOnly: onlineUserOnly ?? false,
        cloudCustomData: cloudCustomData);
    return sendMsgRes;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Share to'),
      ),
      body: RecentForwardList(
        isMultiSelect: false,
        onChanged: (conversationList) async {
          showDialogBox(
            data: jsonEncode(widget.data),
            convId: conversationList.first.conversationID,
            convType: conversationList.first.type == 2 ? ConvType.group : ConvType.c2c,
          );
        },
      ),
    );
  }
}

// ignore_for_file: must_be_immutable, unused_import

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/data_services/conversation/conversation_services.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_search_view_model.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_item.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_folder.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/tim_uikit_search_msg_detail.dart';
import 'package:tencent_cloud_chat_uikit/ui/views/TIMUIKitSearch/pureUI/tim_uikit_search_showAll.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';

import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';

class TIMUIKitSearchMsg extends TIMUIKitStatelessWidget {
  List<V2TimMessageSearchResultItem?> msgList;
  int totalMsgCount;
  String keyword;
  final Function(V2TimConversation, V2TimMessage?) onTapConversation;
  final model = serviceLocator<TUISearchViewModel>();
  final Function(V2TimConversation, String) onEnterConversation;

  TIMUIKitSearchMsg(
      {required this.msgList,
      required this.keyword,
      required this.totalMsgCount,
      super.key,
      required this.onTapConversation,
      required this.onEnterConversation});

  Widget _renderShowALl(bool isShowMore) {
    return (isShowMore == true)
        ? TIMUIKitSearchShowALl(
            textShow: TIM_t("更多聊天记录"),
            onClick: () => {model.searchMsgByKey(keyword, false)},
          )
        : Container();
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    List<V2TimConversation?> conversationList =
        Provider.of<TUISearchViewModel>(context).conversationList;

    if (msgList.isNotEmpty) {
      return TIMUIKitSearchFolder(folderName: TIM_t("聊天记录"), children: [
        ...msgList.map((conv) {
          V2TimConversation? conversation;
          final index = conversationList.indexWhere(
                  (item) => item!.conversationID == conv?.conversationID);
          if(index > -1){
            conversation = conversationList[index]!;
          }
          if(conversation == null){
            return Container();
          }
          final option1 = conv?.messageCount;
          return TIMUIKitSearchItem(
            onClick: () async {
              onEnterConversation(conversation!, keyword);
            },
            faceUrl: conversation.faceUrl ?? "",
            showName: conversation.showName ?? "",
            lineOne: conversation.showName ?? "",
            lineTwo: TIM_t_para("{{option1}}条相关聊天记录", "$option1条相关聊天记录")(
                option1: option1),
          );
        }),
        _renderShowALl(totalMsgCount > msgList.length)
      ]);
    } else {
      return Container();
    }
  }
}

import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_audio_waveforms/flutter_audio_waveforms.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:just_audio/just_audio.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_chat_separate_view_model.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_chat_global_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/message/message_services.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/ui/constants/history_message_constant.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/sound_record.dart';
import 'package:tencent_im_base/tencent_im_base.dart';

import 'TIMUIKitMessageReaction/tim_uikit_message_reaction_show_panel.dart';

class TIMUIKitSoundElem extends StatefulWidget {
  final V2TimMessage message;
  final V2TimSoundElem soundElem;
  final String msgID;
  final bool isFromSelf;
  final int? localCustomInt;
  final bool isShowJump;
  final VoidCallback? clearJump;
  final TextStyle? fontStyle;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? textPadding;
  final bool? isShowMessageReaction;
  final TUIChatSeparateViewModel chatModel;

  const TIMUIKitSoundElem(
      {super.key,
      required this.soundElem,
      required this.msgID,
      required this.isFromSelf,
      this.isShowJump = false,
      this.clearJump,
      this.localCustomInt,
      this.fontStyle,
      this.borderRadius,
      this.backgroundColor,
      this.textPadding,
      required this.message,
      this.isShowMessageReaction,
      required this.chatModel});

  @override
  State<StatefulWidget> createState() => _TIMUIKitSoundElemState();
}

class _TIMUIKitSoundElemState extends TIMUIKitState<TIMUIKitSoundElem> {
  final int charLen = 8;
  bool isPlaying = false;
  StreamSubscription<Object>? subscription;
  bool isShowJumpState = false;
  bool isShining = false;
  final TUIChatGlobalModel globalModel = serviceLocator<TUIChatGlobalModel>();
  final MessageService _messageService = serviceLocator<MessageService>();
  late V2TimSoundElem stateElement = widget.message.soundElem!;
  final ValueNotifier<bool> isLoadingNotifier = ValueNotifier<bool>(false);

  final samples = List.generate(50, (index) => Random().nextDouble() * 1 + 1);
  Duration _activeDuration = Duration.zero;

  _playSound() async {
    if (!SoundPlayer.isInit) {
      SoundPlayer.initSoundPlayer();
    }
    if (widget.localCustomInt == null || widget.localCustomInt != HistoryMessageDartConstant.read) {
      globalModel.setLocalCustomInt(widget.msgID, HistoryMessageDartConstant.read, widget.chatModel.conversationID);
    }
    if (isPlaying) {
      SoundPlayer.pause();
      setState(() {
        isPlaying = false;
      });
    } else {
      if (widget.chatModel.currentPlayedMsgId != '') {
        if (widget.chatModel.currentPlayedMsgId != widget.msgID) {
          SoundPlayer.play(url: stateElement.url!);
          widget.chatModel.currentPlayedMsgId = widget.msgID;
          setState(() {
            isPlaying = true;
          });
          return;
        } else {
          SoundPlayer.resume();
          setState(() {
            isPlaying = true;
          });
          return;
        }
      }
      SoundPlayer.play(url: stateElement.url!);
      widget.chatModel.currentPlayedMsgId = widget.msgID;

      setState(() {
        isPlaying = widget.chatModel.currentPlayedMsgId != '' && widget.chatModel.currentPlayedMsgId == widget.msgID;
      });
    }
  }

  downloadMessageDetailAndSave() async {
    if (widget.message.msgID != null && widget.message.msgID != '') {
      if (widget.message.soundElem!.url == null || widget.message.soundElem!.url == '') {
        final response = await _messageService.getMessageOnlineUrl(msgID: widget.message.msgID!);
        if (response.data != null) {
          widget.message.soundElem = response.data!.soundElem;
          Future.delayed(const Duration(microseconds: 10), () {
            setState(() => stateElement = response.data!.soundElem!);
          });
        }
      }
      if (!PlatformUtils().isWeb) {
        if (widget.message.soundElem!.localUrl == null || widget.message.soundElem!.localUrl == '') {
          _messageService.downloadMessage(
              msgID: widget.message.msgID!, messageType: 4, imageType: 0, isSnapshot: false);
        }
      }
    }
  }

  StreamSubscription<Duration>? positionSubscription;

  @override
  void initState() {
    super.initState();

    subscription = SoundPlayer.playStateListener(listener: (PlayerState state) {
      updatePlayingState(state.processingState);
      if (state.processingState == ProcessingState.completed) {
        widget.chatModel.currentPlayedMsgId = "";
        setState(() {
          isPlaying = false;
        });
      }
    });

    SoundPlayer.playerStateStream.listen((event) {
      if (event.processingState == ProcessingState.idle && widget.chatModel.currentPlayedMsgId != widget.msgID) {
        setState(() {
          isPlaying = false;
        });
      }
    });

    positionSubscription = SoundPlayer.positionStream.listen((position) {
      if (isPlaying) {
        setState(() {
          _activeDuration = position;
        });
      }
    });

    downloadMessageDetailAndSave();
  }

  void updatePlayingState(ProcessingState state) {
    switch (state) {
      case ProcessingState.loading:
        isLoadingNotifier.value = true;
        break;
      default:
        isLoadingNotifier.value = false;
        break;
    }
  }

  @override
  void dispose() {
    if (isPlaying) {
      SoundPlayer.stop();
      widget.chatModel.currentPlayedMsgId = "";
    }
    subscription?.cancel();
    isLoadingNotifier.dispose();
    super.dispose();
  }

  double _getSoundLen() {
    double soundLen = 32;
    if (stateElement.duration != null) {
      final realSoundLen = stateElement.duration!;
      int sdLen = 32;
      if (realSoundLen > 10) {
        sdLen = 12 * charLen + ((realSoundLen - 10) * charLen / 0.5).floor();
      } else if (realSoundLen > 2) {
        sdLen = 2 * charLen + realSoundLen * charLen;
      }
      sdLen = min(sdLen, 20 * charLen);
      soundLen = sdLen.toDouble();
    }

    return soundLen;
  }

  _showJumpColor() {
    if ((widget.chatModel.jumpMsgID != widget.message.msgID) && (widget.message.msgID?.isNotEmpty ?? true)) {
      return;
    }
    isShining = true;
    int shineAmount = 6;
    setState(() {
      isShowJumpState = true;
    });
    Timer.periodic(const Duration(milliseconds: 300), (timer) {
      if (mounted) {
        setState(() {
          isShowJumpState = shineAmount.isOdd ? true : false;
        });
      }
      if (shineAmount == 0 || !mounted) {
        isShining = false;
        timer.cancel();
      }
      shineAmount--;
    });
    widget.clearJump!();
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final theme = value.theme;

    final backgroundColor = widget.isFromSelf
        ? (theme.chatMessageItemFromSelfBgColor ?? theme.lightPrimaryMaterialColor.shade50)
        : (theme.chatMessageItemFromOthersBgColor);

    final borderRadius = widget.isFromSelf
        ? const BorderRadius.only(
            topLeft: Radius.circular(14),
            topRight: Radius.circular(4),
            bottomLeft: Radius.circular(14),
            bottomRight: Radius.circular(14))
        : const BorderRadius.only(
            topLeft: Radius.circular(5),
            topRight: Radius.circular(14),
            bottomLeft: Radius.circular(14),
            bottomRight: Radius.circular(14));
    if (widget.isShowJump) {
      if (!isShining) {
        Future.delayed(Duration.zero, () {
          _showJumpColor();
        });
      } else {
        if ((widget.chatModel.jumpMsgID == widget.message.msgID) && (widget.message.msgID?.isNotEmpty ?? false)) {
          widget.clearJump!();
        }
      }
    }

    final dur = stateElement.duration ?? 0;

    return GestureDetector(
      onTap: () => _playSound(),
      child: Container(
        padding: widget.textPadding ?? const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: isShowJumpState ? const Color.fromRGBO(245, 166, 35, 1) : (widget.backgroundColor ?? backgroundColor),
          borderRadius: widget.borderRadius ?? borderRadius,
        ),
        constraints: const BoxConstraints(maxWidth: 240),
        child: Column(
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ValueListenableBuilder(
                    valueListenable: isLoadingNotifier,
                    builder: (context, isLoading, _) {
                      if (isLoading && widget.chatModel.currentPlayedMsgId == widget.msgID) {
                        return SizedBox(
                          width: 24,
                          height: 24,
                          child: Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: CircularProgressIndicator(
                              color: (widget.isFromSelf ? theme.white : theme.black)?.withValues(alpha: 0.8),
                              strokeWidth: 2,
                            ),
                          ),
                        );
                      }
                      return Icon(
                        isPlaying ? LucideIcons.circle_pause : LucideIcons.circle_play,
                        color: (widget.isFromSelf ? theme.white : theme.black)?.withValues(alpha: 0.8),
                      );
                    }),
                const SizedBox(width: 6),
                SquigglyWaveform(
                  maxDuration: Duration(seconds: dur),
                  elapsedDuration: Duration(
                    seconds: min(_activeDuration.inSeconds, dur),
                  ),
                  samples: samples,
                  height: 20,
                  width: 100,
                  inactiveColor: (widget.isFromSelf ? (theme.white ?? Colors.white) : (theme.black ?? Colors.black))
                      .withValues(alpha: 0.3),
                  activeColor: (widget.isFromSelf ? (theme.white ?? Colors.white) : (theme.black ?? Colors.black))
                      .withValues(alpha: 0.9),
                ),
                const SizedBox(width: 6),
                Text(
                  "${(dur ~/ 60)}:${(dur % 60).toString().padLeft(2, '0')}",
                  style: TextStyle(
                    fontSize: 10,
                    color: (widget.isFromSelf ? theme.white : theme.black)?.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
            if (widget.isShowMessageReaction ?? true)
              TIMUIKitMessageReactionShowPanel(
                message: widget.message,
              )
          ],
        ),
      ),
    );
  }
}

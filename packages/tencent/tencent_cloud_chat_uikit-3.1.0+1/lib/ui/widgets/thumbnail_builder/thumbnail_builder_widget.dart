import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/thumbnail_builder/thumbnail_builder.dart';

class VideoThumbnailBuilder extends StatefulWidget {
  final String videoUrl;
  final double width;
  final double height;

  const VideoThumbnailBuilder({super.key, required this.videoUrl, this.width = 100, this.height = 100});

  @override
  State<VideoThumbnailBuilder> createState() => _VideoThumbnailBuilderState();
}

class _VideoThumbnailBuilderState extends State<VideoThumbnailBuilder> {
  final ThumbnailBuilder thumbnailBuilder = ThumbnailBuilder.instance;
  Future<Uint8List?>? _thumbnailFuture;

  @override
  void initState() {
    super.initState();
    _thumbnailFuture = thumbnailBuilder.getThumbnail(widget.videoUrl);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Uint8List?>(
      future: _thumbnailFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return SizedBox(
            width: widget.width,
            height: widget.height,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasData && snapshot.data != null) {
          return Stack(
            alignment: Alignment.center,
            children: [
              Positioned.fill(
                child: Image.memory(
                  snapshot.data!,
                  width: widget.width,
                  height: widget.height,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(Icons.error, size: widget.width);
                  },
                ),
              ),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withOpacity(0.5),
                ),
                child: const SizedBox(
                  width: 24,
                  height: 24,
                  child: Icon(Icons.play_arrow, color: Colors.white),
                ),
              ),
            ],
          );
        }

        return Icon(Icons.video_file, size: widget.width);
      },
    );
  }
}

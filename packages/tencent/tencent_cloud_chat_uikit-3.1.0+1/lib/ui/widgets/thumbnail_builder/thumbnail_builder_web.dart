import 'dart:async';
import 'dart:convert';
import 'dart:html' as html;

import 'package:flutter/foundation.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/thumbnail_builder/thumbnail_builder.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/thumbnail_builder/thumbnail_cache.dart';

ThumbnailBuilder getThumbnailBuilder() => ThumbnailBuilderWeb();

class ThumbnailBuilderWeb extends ThumbnailBuilder {
  @override
  Future<Uint8List?> getThumbnail(String videoUrl) async {
    if (!kIsWeb) return null;

    // Check cache first
    final cachedThumbnail = ThumbnailCache.get(videoUrl);
    if (cachedThumbnail != null) {
      return cachedThumbnail;
    }

    final completer = Completer<Uint8List?>();

    final video = html.VideoElement()
      ..src = videoUrl
      ..crossOrigin = 'Anonymous'
      ..preload = 'metadata';

    video.onLoadedData.listen((_) {
      final canvas = html.CanvasElement(width: 100,height: 100);
      final ctx = canvas.context2D;

      // Set current time to middle of video to avoid first frame
        video.currentTime = video.duration / 2;
    });

    video.onSeeked.listen((_) {
      final canvas = html.CanvasElement();
      final ctx = canvas.context2D;

      ctx.drawImage(video, 0, 0);

      try {
        final dataUrl = canvas.toDataUrl('image/jpeg');
        final bytes = base64Decode(dataUrl.split(',').last);
        ThumbnailCache.put(videoUrl, bytes); // Cache the thumbnail
        completer.complete(bytes);
      } catch (e) {
        print('Thumbnail generation error: $e');
        completer.complete(null);
      }
    });

    video.onError.listen((event) {
      print('Video loading error');
      completer.complete(null);
    });

    return completer.future;
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:pointer_interceptor/pointer_interceptor.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_chat_separate_view_model.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_chat_global_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/constants/history_message_constant.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/file_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/logger.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/message.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/permission.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/image_hero.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/multi_image/pic_swiper_pluigin.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/video_screen_swiper.dart';
import 'package:universal_html/html.dart' as html;

import '../image_screen.dart';

class PicSwiper extends StatefulWidget {
  const PicSwiper({
    super.key,
    required this.index,
    this.imageMessages = const [],
    required this.theme,
    required this.chatModel,
  });
  final int index;
  final List<V2TimMessage> imageMessages;
  final TUITheme theme;
  final TUIChatSeparateViewModel chatModel;
  @override
  _PicSwiperState createState() => _PicSwiperState();
}

class _PicSwiperState extends TIMState<PicSwiper> with TickerProviderStateMixin {
  final TUIChatGlobalModel model = serviceLocator<TUIChatGlobalModel>();
  final StreamController<int> rebuildIndex = StreamController<int>.broadcast();
  final StreamController<bool> rebuildSwiper = StreamController<bool>.broadcast();
  final StreamController<double> rebuildDetail = StreamController<double>.broadcast();
  final Map<int, ImageDetailInfo> detailKeys = <int, ImageDetailInfo>{};
  late AnimationController _doubleClickAnimationController;
  late AnimationController _slideEndAnimationController;
  late Animation<double> _slideEndAnimation;
  Animation<double>? _doubleClickAnimation;
  late DoubleClickAnimationListener _doubleClickAnimationListener;
  List<double> doubleTapScales = <double>[1.0, 2.0];
  GlobalKey<ExtendedImageSlidePageState> slidePagekey = GlobalKey<ExtendedImageSlidePageState>();
  int _currentIndex = 0;
  bool _showSwiper = true;
  double _imageDetailY = 0;
  Rect? imageDRect;
  bool isLoading = false;
  // final List<int> _cachedIndexes = <int>[];
  // @override
  // void didChangeDependencies() {
  //   super.didChangeDependencies();
  //   _preloadImage(widget.index! - 1);
  //   _preloadImage(widget.index! + 1);
  // }

  // void _preloadImage(int index) {
  //   if (_cachedIndexes.contains(index)) {
  //     return;
  //   }
  //   if (0 <= index && index < widget.imageMessages.length) {
  //     final String url = widget.imageMessages[index].imageElem?.imageList?.first?.url ?? "";

  //     precacheImage(
  //       ExtendedNetworkImageProvider(
  //         url,
  //         cache: true,
  //         imageCacheName: 'CropImage',
  //       ),
  //       context,
  //     );

  //     _cachedIndexes.add(index);
  //   }
  // }

  late ExtendedPageController _pageController;

  Widget errorDisplay(BuildContext context, TUITheme? theme) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: const BorderRadius.all(Radius.circular(5)),
          border: Border.all(
            width: 2,
            color: theme?.weakDividerColor ?? Colors.grey,
          )),
      height: 170,
      width: 170,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingAnimationWidget.staggeredDotsWave(
              color: theme?.weakTextColor ?? Colors.grey,
              size: 28,
            )
          ],
        ),
      ),
    );
  }

  String getOriginImgURL(V2TimMessage message) {
    // 实际拿的是原图
    V2TimImage? img =
        MessageUtils.getImageFromImgList(message.imageElem!.imageList, HistoryMessageDartConstant.oriImgPrior);
    return img == null ? message.imageElem!.path! : img.url!;
  }

  Future<void> _saveImg(TUITheme theme, V2TimMessage message) async {
    try {
      String? imageUrl;
      bool isAssetBool = false;
      final imageElem = message.imageElem;

      if (imageElem != null) {
        final originUrl = getOriginImgURL(message);
        final localUrl = imageElem.imageList?.firstOrNull?.localUrl;
        final filePath = imageElem.path;
        final isWeb = PlatformUtils().isWeb;
        final file = FileUtils.getFile(filePath!);
        if (!isWeb && file.existsSync()) {
          imageUrl = filePath;
          isAssetBool = true;
        } else if (localUrl != null && (!isWeb && FileUtils.getFile(localUrl).existsSync())) {
          imageUrl = localUrl;
          isAssetBool = true;
        } else {
          imageUrl = originUrl;
          isAssetBool = false;
        }
      }

      if (imageUrl != null) {
        return await _saveImageToLocal(
          context,
          imageUrl,
          isLocalResource: isAssetBool,
          theme: theme,
          message: message,
        );
      }
    } catch (e) {
      onTIMCallback(TIMCallback(infoCode: 6660414, infoRecommendText: TIM_t("正在下载中"), type: TIMCallbackType.INFO));
      return;
    }
  }

  Future<void> _saveImageToLocal(
    context,
    String imageUrl, {
    bool isLocalResource = true,
    TUITheme? theme,
    required V2TimMessage message,
  }) async {
    if (PlatformUtils().isWeb) {
      download(imageUrl) async {
        final http.Response r = await http.get(Uri.parse(imageUrl));
        final data = r.bodyBytes;
        final base64data = base64Encode(data);
        final a = html.AnchorElement(href: 'data:image/jpeg;base64,$base64data');
        a.download = md5.convert(utf8.encode(imageUrl)).toString();
        a.click();
        a.remove();
      }

      download(imageUrl);
      return;
    }
    bool hasPermission = false;

    if (PlatformUtils().isIOS) {
      hasPermission = await Permissions.checkPermission(context, Permission.photosAddOnly.value, theme, false);
    } else if (PlatformUtils().isAndroid) {
      hasPermission = await Permissions.checkPermission(
          context,
          Permissions.PERMISSION_PHOTOS, // Use the new constant
          theme,
          false);
    }

    if (!hasPermission) {
      return;
    }

    if (!isLocalResource) {
      if (message.msgID == null || message.msgID!.isEmpty) {
        return;
      }

      if (model.getMessageProgress(message.msgID) == 100) {
        String savePath;
        final file = FileUtils.getFile(message.imageElem!.path!);
        if (message.imageElem!.path != null && message.imageElem!.path != '' && file.existsSync()) {
          savePath = message.imageElem!.path!;
        } else {
          savePath = model.getFileMessageLocation(message.msgID);
        }
        final f = FileUtils.getFile(savePath);
        if (f.existsSync()) {
          var result = await ImageGallerySaverPlus.saveFile(savePath);

          if (PlatformUtils().isIOS) {
            if (result['isSuccess']) {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("图片保存成功"), infoCode: 6660406));
            } else {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("图片保存失败"), infoCode: 6660407));
            }
          } else {
            if (result != null) {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("图片保存成功"), infoCode: 6660406));
            } else {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("图片保存失败"), infoCode: 6660407));
            }
          }
          return;
        }
      } else {
        onTIMCallback(TIMCallback(
            type: TIMCallbackType.INFO, infoRecommendText: TIM_t("the message is downloading"), infoCode: -1));
      }
      return;
    }

    var result = await ImageGallerySaverPlus.saveFile(imageUrl);

    if (PlatformUtils().isIOS) {
      if (result['isSuccess']) {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("图片保存成功"), infoCode: 6660406));
      } else {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("图片保存失败"), infoCode: 6660407));
      }
    } else {
      if (result != null) {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("图片保存成功"), infoCode: 6660406));
        TUIToast.show(content: '图片保存成功');
      } else {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("图片保存失败"), infoCode: 6660407));
      }
    }
    return;
  }

  Widget errorPage(theme) => Container(
      height: MediaQuery.of(context).size.height,
      color: theme.black,
      child: GestureDetector(
        onTap: () {
          Navigator.of(context).pop();
        },
        child: errorDisplay(context, theme),
      ));
  @override
  Widget timBuild(BuildContext context) {
    void close() {
      slidePagekey.currentState!.popPage();
      Navigator.pop(context);
    }

    final Size size = MediaQuery.of(context).size;
    imageDRect = Offset.zero & size;
    Widget result = Material(
      /// if you use ExtendedImageSlidePage and slideType =SlideType.onlyImage,
      /// make sure your page is transparent background
      color: Colors.transparent,
      shadowColor: Colors.transparent,
      child: Stack(
        fit: StackFit.expand,
        children: <Widget>[
          ExtendedImageGesturePageView.builder(
            controller: _pageController,
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            canScrollPage: (GestureDetails? gestureDetails) {
              return _imageDetailY >= 0;
              //return (gestureDetails?.totalScale ?? 1.0) <= 1.0;
            },
            itemBuilder: (BuildContext context, int index) {
              final V2TimMessage message = widget.imageMessages[index];
              final heroTag =
                  "${message.msgID ?? message.id ?? message.timestamp ?? DateTime.now().millisecondsSinceEpoch}null${widget.index}";
              final isVideo = message.videoElem != null;
              if (isVideo) {
                return HeroWidget(
                  tag: heroTag,
                  slidePagekey: slidePagekey,
                  slideType: SlideType.onlyImage,
                  child: VideoScreen2(
                    heroTag: '',
                    message: widget.imageMessages[index],
                    videoElement: widget.imageMessages[index].videoElem!,
                  ),
                );
              }
              V2TimImage? getImageFromList(V2TimImageTypesEnum imgType) {
                V2TimImage? img = MessageUtils.getImageFromImgList(widget.imageMessages[index].imageElem!.imageList,
                    HistoryMessageDartConstant.imgPriorMap[imgType] ?? HistoryMessageDartConstant.oriImgPrior);

                return img;
              }

              final String item = widget.imageMessages[index].imageElem?.path ?? '';

              V2TimImage? originalImg = getImageFromList(V2TimImageTypesEnum.original);
              V2TimImage? smallImg = getImageFromList(V2TimImageTypesEnum.small);

              Widget renderAllImage(
                  {double? positionRadio,
                  required TUITheme theme,
                  bool isNetworkImage = false,
                  String? webPath,
                  V2TimImage? originalImg,
                  V2TimImage? smallImg,
                  String? smallLocalPath,
                  String? originLocalPath}) {
                Widget getImageWidget() {
                  if (isNetworkImage) {
                    return PlatformUtils().isWeb
                        ? ExtendedImage.network(
                            webPath ?? smallImg?.url ?? originalImg!.url!,
                            enableSlideOutPage: true,
                            mode: ExtendedImageMode.gesture,
                            imageCacheName: 'CropImage',
                          )
                        : ExtendedImage.network(
                            // alignment: Alignment.topCenter,
                            webPath ?? smallImg?.url ?? originalImg!.url!,
                            enableSlideOutPage: true,
                            mode: ExtendedImageMode.gesture,
                            imageCacheName: 'CropImage', initGestureConfigHandler: (ExtendedImageState state) {
                            double? initialScale = 1.0;

                            if (state.extendedImageInfo != null) {
                              initialScale = initScale(
                                  size: size,
                                  initialScale: initialScale,
                                  imageSize: Size(state.extendedImageInfo!.image.width.toDouble(),
                                      state.extendedImageInfo!.image.height.toDouble()));
                            }
                            return GestureConfig(
                              inPageView: true,
                              initialScale: initialScale ?? 1.0,
                              maxScale: max(initialScale ?? 1.0, 5.0),
                              animationMaxScale: max(initialScale ?? 1.0, 5.0),
                              initialAlignment: InitialAlignment.center,
                              //you can cache gesture state even though page view page change.
                              //remember call clearGestureDetailsCache() method at the right time.(for example,this page dispose)
                              cacheGesture: false,
                            );
                          }, onDoubleTap: (ExtendedImageGestureState state) {
                            ///you can use define pointerDownPosition as you can,
                            ///default value is double tap pointer down postion.
                            final Offset? pointerDownPosition = state.pointerDownPosition;
                            final double? begin = state.gestureDetails!.totalScale;
                            double end;

                            //remove old
                            _doubleClickAnimation?.removeListener(_doubleClickAnimationListener);

                            //stop pre
                            _doubleClickAnimationController.stop();

                            //reset to use
                            _doubleClickAnimationController.reset();

                            if (begin == doubleTapScales[0]) {
                              end = doubleTapScales[1];
                            } else {
                              end = doubleTapScales[0];
                            }

                            _doubleClickAnimationListener = () {
                              //print(_animation.value);
                              state.handleDoubleTap(
                                  scale: _doubleClickAnimation!.value, doubleTapPosition: pointerDownPosition);
                            };
                            _doubleClickAnimation =
                                _doubleClickAnimationController.drive(Tween<double>(begin: begin, end: end));

                            _doubleClickAnimation!.addListener(_doubleClickAnimationListener);

                            _doubleClickAnimationController.forward();
                          }
                            // errorWidget: (context, error, stackTrace) => errorPage(theme),
                            // cacheKey: smallImg?.uuid ?? originalImg!.uuid,
                            // progressIndicatorBuilder: (context, url, downloadProgress) => Center(
                            //   child: Container(
                            //     decoration: BoxDecoration(
                            //       borderRadius: BorderRadius.circular(12),
                            //       color: Colors.grey.withOpacity(0.4),
                            //     ),
                            //     child: Center(
                            //       child: CircularProgressIndicator.adaptive(
                            //         value: downloadProgress.progress,
                            //         backgroundColor: const Color(0xFFEFD8B7),
                            //       ),
                            //     ),
                            //   ),
                            // ),
                            // placeholder: (context, url) => Image(image: MemoryImage(kTransparentImage)),
                            // fadeInDuration: const Duration(milliseconds: 0),
                            );
                  } else {
                    final imgPath =
                        (TencentUtils.checkString(smallLocalPath) != null ? smallLocalPath : originLocalPath)!;

                    return ExtendedImage.file(
                      FileUtils.getFile(imgPath),
                      fit: BoxFit.contain,
                      enableSlideOutPage: true,
                      mode: ExtendedImageMode.gesture,
                      imageCacheName: 'CropImage',
                      //layoutInsets: EdgeInsets.all(20),
                      initGestureConfigHandler: (ExtendedImageState state) {
                        double? initialScale = 1.0;

                        if (state.extendedImageInfo != null) {
                          initialScale = initScale(
                              size: size,
                              initialScale: initialScale,
                              imageSize: Size(state.extendedImageInfo!.image.width.toDouble(),
                                  state.extendedImageInfo!.image.height.toDouble()));
                        }
                        return GestureConfig(
                          inPageView: true,
                          initialScale: initialScale ?? 1.0,
                          maxScale: max(initialScale ?? 1.0, 5.0),
                          animationMaxScale: max(initialScale ?? 1.0, 5.0),
                          initialAlignment: InitialAlignment.center,
                          //you can cache gesture state even though page view page change.
                          //remember call clearGestureDetailsCache() method at the right time.(for example,this page dispose)
                          cacheGesture: false,
                        );
                      },
                      onDoubleTap: (ExtendedImageGestureState state) {
                        ///you can use define pointerDownPosition as you can,
                        ///default value is double tap pointer down postion.
                        final Offset? pointerDownPosition = state.pointerDownPosition;
                        final double? begin = state.gestureDetails!.totalScale;
                        double end;

                        //remove old
                        _doubleClickAnimation?.removeListener(_doubleClickAnimationListener);

                        //stop pre
                        _doubleClickAnimationController.stop();

                        //reset to use
                        _doubleClickAnimationController.reset();

                        if (begin == doubleTapScales[0]) {
                          end = doubleTapScales[1];
                        } else {
                          end = doubleTapScales[0];
                        }

                        _doubleClickAnimationListener = () {
                          //print(_animation.value);
                          state.handleDoubleTap(
                              scale: _doubleClickAnimation!.value, doubleTapPosition: pointerDownPosition);
                        };
                        _doubleClickAnimation =
                            _doubleClickAnimationController.drive(Tween<double>(begin: begin, end: end));

                        _doubleClickAnimation!.addListener(_doubleClickAnimationListener);

                        _doubleClickAnimationController.forward();
                      },
                      loadStateChanged: (ExtendedImageState state) {
                        if (state.extendedImageLoadState == LoadState.completed) {
                          final Rect imageDRect = getDestinationRect(
                            rect: Offset.zero & size,
                            inputSize: Size(
                              state.extendedImageInfo!.image.width.toDouble(),
                              state.extendedImageInfo!.image.height.toDouble(),
                            ),
                            fit: BoxFit.contain,
                          );

                          detailKeys[index] ??= ImageDetailInfo(
                            imageDRect: imageDRect,
                            pageSize: size,
                            imageInfo: state.extendedImageInfo!,
                          );
                          // final ImageDetailInfo? imageDetailInfo = detailKeys[index];
                          return StreamBuilder<double>(
                            builder: (BuildContext context, AsyncSnapshot<double> data) {
                              return ExtendedImageGesture(
                                state,
                                canScaleImage: (_) => _imageDetailY == 0,
                                imageBuilder: (
                                  Widget image, {
                                  ExtendedImageGestureState? imageGestureState,
                                }) {
                                  return Stack(children: <Widget>[
                                    Positioned.fill(
                                      top: _imageDetailY,
                                      bottom: -_imageDetailY,
                                      child: image,
                                    ),
                                  ]);
                                },
                              );
                            },
                            initialData: _imageDetailY,
                            stream: rebuildDetail.stream,
                          );
                        }
                        return null;
                      },
                    );
                  }
                }

                return getImageWidget();
              }

              Widget renderImage(TUITheme theme, {V2TimImage? originalImg, V2TimImage? smallImg}) {
                double positionRadio = 1.0;

                if (smallImg?.width != null &&
                    smallImg?.height != null &&
                    smallImg?.width != 0 &&
                    smallImg?.height != 0) {
                  positionRadio = (smallImg!.width! / smallImg.height!);
                }

                if (PlatformUtils().isWeb && message.imageElem!.path != null) {
                  // Displaying on Web only
                  return renderAllImage(
                      theme: theme,
                      isNetworkImage: true,
                      smallImg: smallImg,
                      originalImg: originalImg,
                      positionRadio: positionRadio,
                      webPath: message.imageElem!.path);
                }

                try {
                  if ((message.isSelf! &&
                      message.imageElem!.path != null &&
                      message.imageElem!.path!.isNotEmpty &&
                      FileUtils.getFile(message.imageElem!.path!).existsSync())) {
                    return renderAllImage(
                      smallLocalPath: message.imageElem!.path!,
                      theme: theme,
                      positionRadio: positionRadio,
                      originLocalPath: message.imageElem!.path!,
                    );
                  }
                } catch (e) {
                  // ignore: avoid_print
                  outputLogger.i(e.toString());
                }

                // try {
                //   if ((TencentUtils.checkString(smallImg?.localUrl) != null &&
                //           FileUtils.getFile(smallImg!.localUrl!).existsSync()) ||
                //       (TencentUtils.checkString(originalImg?.localUrl) != null &&
                //           FileUtils.getFile(originalImg!.localUrl!).existsSync())) {
                //     return _renderAllImage(
                //         smallLocalPath: smallImg?.localUrl ?? "",
                //         theme: theme,
                //         positionRadio: positionRadio,
                //         originLocalPath: originalImg?.localUrl);
                //   }
                // } catch (e) {
                // ignore: avoid_print
                outputLogger.i(e.toString());
                return renderAllImage(
                    theme: theme,
                    isNetworkImage: true,
                    smallImg: smallImg,
                    positionRadio: positionRadio,
                    originalImg: originalImg);
                // }

                if ((smallImg?.url ?? originalImg?.url) != null && (smallImg?.url ?? originalImg?.url)!.isNotEmpty) {
                  return renderAllImage(
                      theme: theme,
                      isNetworkImage: true,
                      positionRadio: positionRadio,
                      smallImg: smallImg,
                      originalImg: originalImg);
                }

                return errorDisplay(context, theme);
              } //     "${message.msgID ?? message.id ?? message.timestamp ?? DateTime.now().millisecondsSinceEpoch}null$imageIndex";

              Widget image;
              image = HeroWidget(
                tag: heroTag,
                slideType: SlideType.onlyImage,
                slidePagekey: slidePagekey,
                child: renderImage(widget.theme, originalImg: originalImg, smallImg: smallImg),
              );
              // image = _renderImage( widget.theme, originalImg: originalImg, smallImg: smallImg);
              image = GestureDetector(
                child: image,
                onTap: () {
                  if (_imageDetailY != 0) {
                    _imageDetailY = 0;
                    rebuildDetail.sink.add(_imageDetailY);
                  } else {
                    slidePagekey.currentState!.popPage();
                    Navigator.pop(context);
                  }
                },
              );

              return image;
            },
            itemCount: widget.imageMessages.length,
            onPageChanged: (int index) {
              _currentIndex = index;
              rebuildIndex.add(index);
              if (_imageDetailY != 0) {
                _imageDetailY = 0;
                rebuildDetail.sink.add(_imageDetailY);
              }
              _showSwiper = true;
              rebuildSwiper.add(_showSwiper);
              // _preloadImage(index - 1);
              // _preloadImage(index + 1);
            },
          ),
          StreamBuilder<bool>(
            builder: (BuildContext c, AsyncSnapshot<bool> d) {
              if (d.data == null || !d.data!) {
                return Container();
              }

              return Positioned(
                bottom: 10,
                left: 0.0,
                right: 0.0,
                child: MySwiperPlugin(
                  widget.imageMessages,
                  _currentIndex,
                  rebuildIndex,
                  theme: widget.theme,
                  onThumbnailTap: (int index) {
                    _pageController.jumpToPage(index);
                  },
                ),
              );
            },
            initialData: true,
            stream: rebuildSwiper.stream,
          ),
          StreamBuilder<bool>(
            builder: (BuildContext c, AsyncSnapshot<bool> d) {
              if (d.data == null || !d.data!) {
                return Container();
              }

              return Positioned(
                right: 10,
                bottom: PlatformUtils().isWeb ? 110 : 90,
                child: SizedBox(
                  width: 48,
                  height: 48,
                  child: PointerInterceptor(
                    child: IconButton(
                      icon: Image.asset(
                        'images/close.png',
                        package: 'tencent_cloud_chat_uikit',
                      ),
                      iconSize: 30,
                      onPressed: close,
                    ),
                  ),
                ),
              );
            },
            initialData: true,
            stream: rebuildSwiper.stream,
          ),
          // StreamBuilder<bool>(
          //   builder: (BuildContext c, AsyncSnapshot<bool> d) {
          //     if (d.data == null || !d.data!) {
          //       return Container();
          //     }
          //     return Positioned(
          //       right: 10,
          //       top: 50,
          //       child: SizedBox(
          //         width: 48,
          //         height: 48,
          //         child: IconButton(
          //           icon: Image.asset(
          //             'images/download.png',
          //             package: 'tencent_cloud_chat_uikit',
          //           ),
          //           iconSize: 30,
          //           onPressed: () async {
          //             if (PlatformUtils().isWeb) {
          //               final message = widget.imageMessages[_currentIndex];
          //               launchUrl(
          //                 Uri.parse(message.imageElem?.path ?? ""),
          //                 mode: LaunchMode.externalApplication,
          //               );
          //               return;
          //             }
          //             setState(() {
          //               isLoading = true;
          //             });
          //             final message = widget.imageMessages[_currentIndex];
          //             await _saveImg(widget.theme, message);
          //             Future.delayed(const Duration(milliseconds: 200), () {
          //               setState(() {
          //                 isLoading = false;
          //               });
          //             });
          //           },
          //         ),
          //       ),
          //     );
          //   },
          //   initialData: true,
          //   stream: rebuildSwiper.stream,
          // ),
          // if (!PlatformUtils().isWeb)
          //   StreamBuilder<bool>(
          //     builder: (BuildContext c, AsyncSnapshot<bool> d) {
          //       if (d.data == null || !d.data!) {
          //         return Container();
          //       }
          //       return Positioned(
          //         right: 50,
          //         top: 50,
          //         child: SizedBox(
          //           width: 48,
          //           height: 48,
          //           child: IconButton(
          //             icon: Image.asset(
          //               'images/delete.png',
          //               package: 'tencent_cloud_chat_uikit',
          //             ),
          //             iconSize: 30,
          //             onPressed: () async {
          //               showCupertinoModalPopup<String>(
          //                 context: context,
          //                 builder: (BuildContext context) {
          //                   return CupertinoActionSheet(
          //                     title: Text(TIM_t("确定删除已选消息")),
          //                     cancelButton: CupertinoActionSheetAction(
          //                       onPressed: () {
          //                         Navigator.pop(
          //                           context,
          //                           "cancel",
          //                         );
          //                       },
          //                       child: Text(TIM_t("取消")),
          //                       isDefaultAction: false,
          //                     ),
          //                     actions: [
          //                       CupertinoActionSheetAction(
          //                         onPressed: () {
          //                           final message = widget.imageMessages[_currentIndex];
          //                           widget.chatModel.deleteSelectedMsg(msgID: [message.msgID ?? ""]);
          //                           widget.chatModel.updateMultiSelectStatus(false);
          //                           Navigator.pop(
          //                             context,
          //                             "cancel",
          //                           );
          //                           close();
          //                         },
          //                         child: Text(
          //                           TIM_t("删除"),
          //                           style: TextStyle(color: widget.theme.cautionColor),
          //                         ),
          //                         isDefaultAction: false,
          //                       )
          //                     ],
          //                   );
          //                 },
          //               );
          //             },
          //           ),
          //         ),
          //       );
          //     },
          //     initialData: true,
          //     stream: rebuildSwiper.stream,
          //   ),
        ],
      ),
    );

    result = ExtendedImageSlidePage(
      key: slidePagekey,
      slideAxis: SlideAxis.vertical,
      slideType: SlideType.onlyImage,
      slideScaleHandler: (
        Offset offset, {
        ExtendedImageSlidePageState? state,
      }) {
        if (state == null) {
          return null;
        }
        //image is ready and it's not sliding.
        if (detailKeys[_currentIndex] != null && state.scale == 1.0) {
          //don't slide page if scale of image is more than 1.0
          if (state.imageGestureState!.gestureDetails!.totalScale! > 1.0) {
            return 1.0;
          }
          //or slide down into detail mode
          if (offset.dy < 0 || _imageDetailY < 0) {
            return 1.0;
          }
        }

        return null;
      },
      slideOffsetHandler: (
        Offset offset, {
        ExtendedImageSlidePageState? state,
      }) {
        if (state == null) {
          return null;
        }
        //image is ready and it's not sliding.
        if (detailKeys[_currentIndex] != null && state.scale == 1.0) {
          //don't slide page if scale of image is more than 1.0

          if (state.imageGestureState!.gestureDetails!.totalScale! > 1.0) {
            print('offset a: $offset');
            return Offset.zero;
          }

          // //or slide down into detail mode
          // if (offset.dy < 0 || _imageDetailY < 0) {
          //   _imageDetailY += offset.dy;

          //   // print(offset.dy);
          //   _imageDetailY = max(-detailKeys[_currentIndex!]!.maxImageDetailY, _imageDetailY);
          //   rebuildDetail.sink.add(_imageDetailY);
          //   print('offset b: $offset');
          //   return Offset.zero;
          // }

          if (_imageDetailY != 0) {
            _imageDetailY = 0;
            _showSwiper = true;
            rebuildSwiper.add(_showSwiper);
            rebuildDetail.sink.add(_imageDetailY);
          }
        }
        print('offset null: $offset');
        return null;
      },
      slideEndHandler: (
        Offset offset, {
        ExtendedImageSlidePageState? state,
        ScaleEndDetails? details,
      }) {
        if (state == null || details == null) {
          return null;
        }
        if (_imageDetailY != 0 && state.scale == 1) {
          if (!_slideEndAnimationController.isAnimating) {
// get magnitude from gesture velocity
            final double magnitude = details.velocity.pixelsPerSecond.distance;

            // do a significant magnitude

            if (magnitude.greaterThanOrEqualTo(minMagnitude)) {
              final Offset direction = details.velocity.pixelsPerSecond / magnitude * 1000;

              _slideEndAnimation = _slideEndAnimationController.drive(Tween<double>(
                begin: _imageDetailY,
                end: (_imageDetailY + direction.dy).clamp(-detailKeys[_currentIndex]!.maxImageDetailY, 0.0),
              ));
              _slideEndAnimationController.reset();
              _slideEndAnimationController.forward();
            }
          }
          return false;
        }

        return null;
      },
      onSlidingPage: (ExtendedImageSlidePageState state) {
        ///you can change other widgets' state on page as you want
        ///base on offset/isSliding etc
        //var offset= state.offset;
        final bool showSwiper = !state.isSliding;
        if (showSwiper != _showSwiper) {
          // do not setState directly here, the image state will change,
          // you should only notify the widgets which are needed to change
          // setState(() {
          // _showSwiper = showSwiper;
          // });

          _showSwiper = showSwiper;
          rebuildSwiper.add(_showSwiper);
        }
      },
      child: result,
    );

    return result;
  }

  @override
  void dispose() {
    _pageController.dispose();
    rebuildIndex.close();
    rebuildSwiper.close();
    rebuildDetail.close();
    _doubleClickAnimationController.dispose();
    _slideEndAnimationController.dispose();
    clearGestureDetailsCache();
    //cancelToken?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.index;
    _pageController = ExtendedPageController(
      initialPage: widget.index,
      pageSpacing: 50,
      shouldIgnorePointerWhenScrolling: false,
    );
    _doubleClickAnimationController = AnimationController(duration: const Duration(milliseconds: 150), vsync: this);

    _slideEndAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _slideEndAnimationController.addListener(() {
      _imageDetailY = _slideEndAnimation.value;
      if (_imageDetailY == 0) {
        _showSwiper = true;
        rebuildSwiper.add(_showSwiper);
      }
      rebuildDetail.sink.add(_imageDetailY);
    });
  }
}

double? initScale({
  required Size imageSize,
  required Size size,
  double? initialScale,
}) {
  final double n1 = imageSize.height / imageSize.width;
  final double n2 = size.height / size.width;
  if (n1 > n2) {
    final FittedSizes fittedSizes = applyBoxFit(BoxFit.contain, imageSize, size);
    //final Size sourceSize = fittedSizes.source;
    final Size destinationSize = fittedSizes.destination;
    return size.width / destinationSize.width;
  } else if (n1 / n2 < 1 / 4) {
    final FittedSizes fittedSizes = applyBoxFit(BoxFit.contain, imageSize, size);
    //final Size sourceSize = fittedSizes.source;
    final Size destinationSize = fittedSizes.destination;
    return size.height / destinationSize.height;
  }

  return initialScale;
}

class ImageDetailInfo {
  ImageDetailInfo({
    required this.imageDRect,
    required this.pageSize,
    required this.imageInfo,
  });
  final GlobalKey<State<StatefulWidget>> key = GlobalKey<State>();

  final Rect imageDRect;

  final Size pageSize;

  final ImageInfo imageInfo;

  double? _maxImageDetailY;
  double get imageBottom => imageDRect.bottom - 20;
  double get maxImageDetailY {
    try {
      //
      return _maxImageDetailY ??= max(key.currentContext!.size!.height - (pageSize.height - imageBottom), 0.1);
    } catch (e) {
      //currentContext is not ready
      return 100.0;
    }
  }
}

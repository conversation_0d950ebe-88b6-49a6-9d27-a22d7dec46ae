import 'dart:convert';
import 'dart:io';

import 'package:chewie/chewie.dart';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_chat_global_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/permission.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';
import 'package:universal_html/html.dart' as html;
import 'package:video_player/video_player.dart';
import 'package:webview_flutter/webview_flutter.dart';

class VideoScreen2 extends StatefulWidget {
  const VideoScreen2({required this.message, required this.heroTag, required this.videoElement, super.key});

  final V2TimMessage message;
  final dynamic heroTag;
  final V2TimVideoElem videoElement;

  @override
  State<StatefulWidget> createState() => _VideoScreenState();
}

class _VideoScreenState extends TIMUIKitState<VideoScreen2> {
  late VideoPlayerController videoPlayerController;
  late ChewieController chewieController;
  final TUIChatGlobalModel model = serviceLocator<TUIChatGlobalModel>();
  bool isInit = false;
  WebViewController? webViewController;

  @override
  initState() {
    super.initState();
    if (PlatformUtils().isWeb) {
      _initWebView();
    } else {
      setVideoPlayerController();
    }
    // 允许横屏
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  void _initWebView() {
    final videoUrl = widget.videoElement.videoUrl ?? widget.videoElement.videoPath ?? widget.videoElement.localVideoUrl;

    if (videoUrl == null) {
      return;
    }

    // Create HTML content for video playback
    final htmlContent = '''
      <!DOCTYPE html>
      <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
          body, html { margin: 0; padding: 0; height: 98%; overflow: hidden; background-color: #000; }
          video { width: 100%; height: 98%; object-fit: contain; }
          video::-webkit-media-controls-fullscreen-button {
           display: none;
        }
        </style>
      </head>
      <body>
        <video playsinline webkit-playsinline controls>
          <source src="$videoUrl" type="video/mp4">
          Your browser does not support the video tag.
        </video>
      </body>
      </html>
    ''';

    // Initialize WebView controller
    webViewController = WebViewController()..loadHtmlString(htmlContent);

    setState(() {
      isInit = true;
    });
  }

  //保存网络视频到本地
  Future<void> _saveNetworkVideo(
    context,
    String videoUrl, {
    bool isAsset = true,
  }) async {
    if (PlatformUtils().isWeb) {
      RegExp exp = RegExp(r"((\.){1}[^?]{2,4})");
      String? suffix = exp.allMatches(videoUrl).last.group(0);
      var xhr = html.HttpRequest();
      xhr.open('get', videoUrl);
      xhr.responseType = 'arraybuffer';
      xhr.onLoad.listen((event) {
        final a = html.AnchorElement(href: html.Url.createObjectUrl(html.Blob([xhr.response])));
        a.download = '${md5.convert(utf8.encode(videoUrl)).toString()}$suffix';
        a.click();
        a.remove();
      });
      xhr.send();
      return;
    }
    if (PlatformUtils().isMobile) {
      if (PlatformUtils().isIOS) {
        if (!await Permissions.checkPermission(
          context,
          Permission.photosAddOnly.value,
        )) {
          return;
        }
      } else {
        final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        if ((androidInfo.version.sdkInt) >= 33) {
          final videos = await Permissions.checkPermission(
            context,
            Permission.videos.value,
          );

          if (!videos) {
            return;
          }
        } else {
          final storage = await Permissions.checkPermission(
            context,
            Permission.storage.value,
          );
          if (!storage) {
            return;
          }
        }
      }
    }
    String savePath = videoUrl;
    if (!isAsset) {
      if (widget.message.msgID == null || widget.message.msgID!.isEmpty) {
        return;
      }
      if (model.getMessageProgress(widget.message.msgID) == 100) {
        String savePath;
        if (widget.message.videoElem!.localVideoUrl != null && widget.message.videoElem!.localVideoUrl != '') {
          savePath = widget.message.videoElem!.localVideoUrl!;
        } else {
          savePath = model.getFileMessageLocation(widget.message.msgID);
        }
        File f = File(savePath);
        if (f.existsSync()) {
          var result = await ImageGallerySaverPlus.saveFile(savePath);
          if (PlatformUtils().isIOS) {
            if (result['isSuccess']) {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存成功"), infoCode: 6660402));
            } else {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存失败"), infoCode: 6660403));
            }
          } else {
            if (result != null) {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存成功"), infoCode: 6660402));
            } else {
              onTIMCallback(
                  TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存失败"), infoCode: 6660403));
            }
          }
        }
      } else {
        onTIMCallback(TIMCallback(
            type: TIMCallbackType.INFO, infoRecommendText: TIM_t("the message is downloading"), infoCode: -1));
      }
      return;
    }
    var result = await ImageGallerySaverPlus.saveFile(savePath);
    if (PlatformUtils().isIOS) {
      if (result['isSuccess']) {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存成功"), infoCode: 6660402));
      } else {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存失败"), infoCode: 6660403));
      }
    } else {
      if (result != null) {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存成功"), infoCode: 6660402));
      } else {
        onTIMCallback(TIMCallback(type: TIMCallbackType.INFO, infoRecommendText: TIM_t("视频保存失败"), infoCode: 6660403));
      }
    }
    return;
  }

  Future<void> _saveVideo() async {
    if (PlatformUtils().isWeb) {
      return await _saveNetworkVideo(
        context,
        widget.videoElement.videoPath!,
        isAsset: true,
      );
    }
    if (widget.videoElement.videoPath != '' &&
        widget.videoElement.videoPath != null &&
        File(widget.videoElement.videoPath!).existsSync()) {
      File f = File(widget.videoElement.videoPath!);
      if (f.existsSync()) {
        return await _saveNetworkVideo(
          context,
          widget.videoElement.videoPath!,
          isAsset: true,
        );
      }
    }
    if (widget.videoElement.localVideoUrl != '' && widget.videoElement.localVideoUrl != null) {
      File f = File(widget.videoElement.localVideoUrl!);
      if (f.existsSync()) {
        return await _saveNetworkVideo(
          context,
          widget.videoElement.localVideoUrl!,
          isAsset: true,
        );
      }
    }
    return await _saveNetworkVideo(
      context,
      widget.videoElement.videoUrl!,
      isAsset: false,
    );
  }

  double getVideoHeight() {
    double height = widget.videoElement.snapshotHeight!.toDouble();
    double width = widget.videoElement.snapshotWidth!.toDouble();
    // 横图
    if (width > height) {
      return height * 1.3;
    }
    return height;
  }

  double getVideoWidth() {
    double height = widget.videoElement.snapshotHeight!.toDouble();
    double width = widget.videoElement.snapshotWidth!.toDouble();
    // 横图
    if (width > height) {
      return width * 1.3;
    }
    return width;
  }

  setVideoPlayerController() async {
    // Prevent multiple initializations
    if (!mounted) {
      return;
    }

    // Clean up existing controllers if they exist
    if (isInit) {
      if (videoPlayerController.value.isInitialized) {
        await videoPlayerController.pause();
        await videoPlayerController.dispose();
      }
      chewieController.dispose();
      setState(() {
        isInit = false;
      });
    }

    if (!PlatformUtils().isWeb) {
      if (TencentUtils.checkString(widget.message.msgID) != null && widget.videoElement.localVideoUrl == null) {
        String savePath = model.getFileMessageLocation(widget.message.msgID);
        File f = File(savePath);
        if (f.existsSync()) {
          widget.videoElement.localVideoUrl = savePath;
        }
      }
    }

    VideoPlayerController player = PlatformUtils().isWeb
        ? ((TencentUtils.checkString(widget.videoElement.videoPath) != null) ||
                widget.message.status == MessageStatus.V2TIM_MSG_STATUS_SENDING
            ? VideoPlayerController.networkUrl(
                Uri.parse(widget.videoElement.videoPath!),
              )
            : (TencentUtils.checkString(widget.videoElement.localVideoUrl) == null)
                ? VideoPlayerController.networkUrl(
                    Uri.parse(widget.videoElement.videoUrl!),
                  )
                : VideoPlayerController.networkUrl(
                    Uri.parse(widget.videoElement.localVideoUrl!),
                  ))
        : VideoPlayerController.networkUrl(
            Uri.parse(widget.videoElement.videoPath!),
          );

    try {
      await player.initialize();

      if (!mounted) {
        player.dispose();
        return;
      }

      double aspectRatio = player.value.aspectRatio;
      ChewieController controller = ChewieController(
        videoPlayerController: player,
        autoPlay: true,
        looping: false,
        showControlsOnInitialize: false,
        allowPlaybackSpeedChanging: false,
        aspectRatio: aspectRatio,
        // customControls: VideoCustomControls2(downloadFn: () async {
        //   return await _saveVideo();
        // }),
      );

      if (!mounted) {
        player.dispose();
        controller.dispose();
        return;
      }

      setState(() {
        videoPlayerController = player;
        chewieController = controller;
        isInit = true;
      });
    } catch (e) {
      player.dispose();
    }
  }

  @override
  didUpdateWidget(oldWidget) {
    if (oldWidget.videoElement.videoUrl != widget.videoElement.videoUrl ||
        oldWidget.videoElement.videoPath != widget.videoElement.videoPath) {
      if (PlatformUtils().isWeb) {
        _initWebView();
      } else {
        setVideoPlayerController();
      }
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    if (!PlatformUtils().isWeb && isInit) {
      if (videoPlayerController.value.isInitialized) {
        videoPlayerController.pause();
        videoPlayerController.dispose();
      }
      chewieController.dispose();
    }
    super.dispose();
  }

  Widget _buildWebView() {
    if (!isInit || webViewController == null) {
      return const Center(child: CircularProgressIndicator(color: Colors.white));
    }
    return WebViewWidget(controller: webViewController!);
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    if (PlatformUtils().isWeb) {
      return ExtendedImageSlidePageHandler(
        child: _buildWebView(),
      );
    }

    return ExtendedImageSlidePageHandler(
      child: isInit
          ? Chewie(
              controller: chewieController,
            )
          : const Center(child: CircularProgressIndicator(color: Colors.white)),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';

enum FTradeMarketType {
  /// 大商所
  dalian(0, 'dalian_futures_exchange'),

  /// 鄭商所
  zhengzhou(1, 'zhengzhou_futures_exchange'),

  /// 上期所
  shanghai(2, 'shanghai_futures_exchange'),

  /// 上期能源
  shanghaiEnergy(3, 'shanghai_energy_exchange'),

  /// 中金所
  china(4, 'china_futures_exchange');

  final int idx;
  final String name;

  const FTradeMarketType(this.idx, this.name);

  static String nameFromMarket(String market) {
    if (market == 'DCE') {
      return 'dalian_futures_exchange'.tr();
    }
    if (market == 'CZCE') {
      return 'zhengzhou_futures_exchange'.tr();
    }
    if (market == 'SHFE') {
      return 'shanghai_futures_exchange'.tr();
    }
    if (market == 'INE') {
      return 'shanghai_energy_exchange'.tr();
    }
    if (market == 'CFFEX') {
      return 'china_futures_exchange'.tr();
    }
    return '';
  }

  String stringIdForCloud() {
    switch (this) {
      case FTradeMarketType.dalian:
        return 'DCE';
      case FTradeMarketType.zhengzhou:
        return 'CZCE';
      case FTradeMarketType.shanghai:
        return 'SHFE';
      case FTradeMarketType.shanghaiEnergy:
        return 'INE';
      case FTradeMarketType.china:
        return 'CFFEX';
    }
  }

  /// 通过 idx 获取枚举值
  static FTradeMarketType fromIndex(int index) {
    assert(
      FTradeMarketType.values.any((e) => e.idx == index),
      'FTradeMarketType error index: $index',
    );
    return FTradeMarketType.values.firstWhere(
      (e) => e.idx == index,
      orElse: () => FTradeMarketType.dalian,
    );
  }

  /// 通过 string 获取枚举值
  /// 通过市场代码（如 'DCE'）获取枚举值
  static FTradeMarketType fromMarketCode(String marketCode) {
    switch (marketCode) {
      case 'DCE':
        return FTradeMarketType.dalian;
      case 'CZCE':
        return FTradeMarketType.zhengzhou;
      case 'SHFE':
        return FTradeMarketType.shanghai;
      case 'INE':
        return FTradeMarketType.shanghaiEnergy;
      case 'CFFEX':
        return FTradeMarketType.china;
      default:
        assert(false, 'Invalid market code: $marketCode');
        return FTradeMarketType.dalian;
    }
  }
}

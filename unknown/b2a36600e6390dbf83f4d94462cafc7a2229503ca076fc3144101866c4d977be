import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';


import 'package:gp_stock_app/core/theme/app_themes.dart';

class SubHeading extends StatelessWidget {
  final IconData? icon;
  final String? label;
  final String? value;

  const SubHeading({
    super.key,
    this.icon,
    this.label,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            if (icon != null)
              Icon(
                icon,
                size: 12,
                color: context.colorTheme.textPrimary,
              ),
            if (icon != null) SizedBox(width: 4.gw),
            if (label != null)
              Text(
                label!,
                style: context.textTheme.primary.fs12.w500,
              ),
          ],
        ),
        if (value != null)
          Text(
            value!,
            style: context.textTheme.primary.w500,
          ),
      ],
    );
  }
}

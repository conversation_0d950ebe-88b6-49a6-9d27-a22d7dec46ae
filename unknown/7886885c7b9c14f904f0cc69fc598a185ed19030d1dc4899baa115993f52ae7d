import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';

import 'package:gp_stock_app/shared/widgets/snackbar/snackbar_helper.dart';

import '../../../../shared/constants/enums.dart';
import '../../../../shared/widgets/app_header.dart';
import '../../../../shared/widgets/buttons/custom_material_button.dart';
import '../../../../shared/widgets/text_fields/text_field_widget.dart';
import '../../logic/profile/profile_cubit.dart';
import '../../widgets/settings/settings_appbar.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ProfileEditScreen extends StatelessWidget with AppHeaderMixin {
  final String title;
  final String initialValue;
  final ProfileUpdateField field;
  final int? maxLength;

  const ProfileEditScreen({
    super.key,
    required this.title,
    required this.initialValue,
    required this.field,
    this.maxLength,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: SettingsAppBar(title: '${'update'.tr()} ${title.tr()}'),
      body: _ProfileEditForm(
        title: title,
        initialValue: initialValue,
        field: field,
        maxLength: maxLength,
      ),
    );
  }
}

class _ProfileEditForm extends StatefulWidget {
  final String title;
  final String initialValue;
  final ProfileUpdateField field;
  final int? maxLength;

  const _ProfileEditForm({
    required this.title,
    required this.initialValue,
    required this.field,
    this.maxLength,
  });

  @override
  State<_ProfileEditForm> createState() => _ProfileEditFormState();
}

class _ProfileEditFormState extends State<_ProfileEditForm> {
  late TextEditingController _controller;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _controller.addListener(() {
      // Force rebuild to update character count
      setState(() {});
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _handleSubmit() async {
    if (_isLoading) return;

    final value = _controller.text.trim();
    if (value.isEmpty) {
      showAppSnackBar(
        'fieldRequired'.tr(),
        context,
        snackBarType: SnackBarType.validation,
      );
      return;
    }

    if (widget.maxLength != null && value.length > widget.maxLength!) {
      showAppSnackBar(
        'textTooLong'.tr(),
        context,
        snackBarType: SnackBarType.validation,
      );
      return;
    }

    setState(() => _isLoading = true);

    switch (widget.field) {
      case ProfileUpdateField.nickname:
        await context.read<ProfileCubit>().updateNickname(value);
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.theme.cardColor,
      padding: EdgeInsets.symmetric(horizontal: 16.gw),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          16.verticalSpace,
          Text(
            widget.title,
            style: context.textTheme.regular.fs16.w600,
          ),
          16.verticalSpace,
          TextFieldWidget(
            controller: _controller,
            hintText: widget.title,
            maxLength: widget.maxLength,
            enabled: !_isLoading,
            isDense: false,
          ),
          const Spacer(),
          BlocConsumer<ProfileCubit, ProfileState>(
            listenWhen: (previous, current) =>
                previous.updateStatus != current.updateStatus && current.updatingField == widget.field,
            listener: (context, state) {
              setState(() => _isLoading = state.updateStatus == DataStatus.loading);

              if (state.updateStatus == DataStatus.success) {
                GPEasyLoading.showSuccess(message: 'updatedSuccessfully'.tr());
                Navigator.pop(context);
              }
              if (state.updateStatus == DataStatus.failed) {
                GPEasyLoading.showToast(state.error ?? 'updateFailed'.tr());
              }
            },
            builder: (context, state) {
              return CustomMaterialButton(
                buttonText: 'confirmChanges'.tr(),
                isLoading: _isLoading,
                onPressed: _handleSubmit,
              );
            },
          ),
          24.verticalSpace,
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/features/futures_trade/f_trade_account/logic/f_trade_acct_entrust_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/logic/f_trade_acct_position_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_3_in_1_section_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_all_info_title_view.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/f_trade_buy_sell_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/logic/f_trade_buy_sell_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

enum FTradeAllInfoTitlesType {
  trade('trade'),
  quotation('quotation');

  final String name;

  const FTradeAllInfoTitlesType(this.name);
}

class FTradeAllInfoScreen extends StatefulWidget {
  final FTradeMarketType type;
  final FTradeAllInfoTitlesType titlesType;
  final FTradeListItemModel data;
  const FTradeAllInfoScreen({super.key, required this.type, required this.titlesType, required this.data});

  factory FTradeAllInfoScreen.fromArgs(Object? args) {
    if (args case (int idx, FTradeAllInfoTitlesType titlesType, FTradeListItemModel data)) {
      return FTradeAllInfoScreen(type: FTradeMarketType.fromIndex(idx), titlesType: titlesType, data: data);
    } else {
      assert(false, 'arguments args type error');
      return FTradeAllInfoScreen(
          type: FTradeMarketType.dalian, titlesType: FTradeAllInfoTitlesType.trade, data: FTradeListItemModel());
    }
  }

  @override
  State<FTradeAllInfoScreen> createState() => _FTradeAllInfoScreenState();
}

class _FTradeAllInfoScreenState extends State<FTradeAllInfoScreen> {
  int titlesIdx = 0;

  @override
  void initState() {
    super.initState();
    if (widget.titlesType == FTradeAllInfoTitlesType.trade) {
      titlesIdx = 0;
    }
    if (widget.titlesType == FTradeAllInfoTitlesType.quotation) {
      titlesIdx = 1;
    }
  }

  /// 切换 交易<->行情(3in1)
  /// Switch between Trade<->3in1
  void onChangeAllInfoScreenTitlesAction(int idx) {
    if ([0, 1].contains(idx) == false) {
      assert(false, '$idx Error');
      return;
    }
    // 页面间切换时 缓存数据共享
    setState(() {
      titlesIdx = idx;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: context.theme.cardColor,
        surfaceTintColor: Colors.transparent,
        title: FTradeAllInfoTitleView(
          selectedIndex: titlesIdx,
          selectTab: (selectTitlesIdx) {
            setState(() {
              titlesIdx = selectTitlesIdx;
            });
          },
        ),
      ),
      body: titlesIdx == 0
          ? MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (_) => FTradeKLineCubit(
                    FTradeKLineScrollRepository(),
                  ),
                ),
                BlocProvider(
                  create: (_) => FTradeAcctEntrustCubit(),
                ),
                BlocProvider(
                  create: (_) => FTradeAcctPositionCubit(),
                ),
                BlocProvider(
                  create: (context) => FTradeBuySellCubit(
                    FTradeBuySellState(),
                    widget.data,
                    context.read<FTradeKLineCubit>(),
                    context.read<FTradeAcctPositionCubit>(),
                  ),
                ),
              ],
              child: FTradeBuySellScreen(
                type: widget.type,
                data: widget.data,
                onChangeAllInfoScreenTitlesAction: onChangeAllInfoScreenTitlesAction,
              ),
            )
          : FTrade3In1SectionScreen(
              type: widget.type,
              data: widget.data,
              onChangeAllInfoScreenTitlesAction: onChangeAllInfoScreenTitlesAction,
            ),
    );
  }
}

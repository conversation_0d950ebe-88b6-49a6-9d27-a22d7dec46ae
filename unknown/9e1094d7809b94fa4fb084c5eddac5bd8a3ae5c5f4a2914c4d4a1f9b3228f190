import os
import codecs
import sys

# 定义插入规则：关键词集合 -> 要插入的 import 行
INSERT_RULES = {
    "theme": {
        "keywords": ["context.theme", "context.textTheme", "context.colorTheme"],
        "import_line": "import 'package:gp_stock_app/core/theme/app_themes.dart';"
    },
    "color_opacity": {
        "keywords": ["withNewOpacity"],
        "import_line": "import 'package:gp_stock_app/shared/app/extension/color_extension.dart';"
    }
}

def process_file(file_path):
    with codecs.open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        content = ''.join(lines)

    insert_lines = []

    # 遍历规则，命中关键词且未包含目标 import 时加入
    for rule in INSERT_RULES.values():
        if any(kw in content for kw in rule["keywords"]) and rule["import_line"] not in content:
            insert_lines.append(rule["import_line"])

    if not insert_lines:
        return  # 无需插入

    # 找到最后一行 import 的位置
    last_import_index = -1
    for i, line in enumerate(lines):
        if line.strip().startswith("import "):
            last_import_index = i

    insert_index = last_import_index + 1

    # 按顺序插入（可选 reversed，如果你有特定顺序要求）
    for line in insert_lines:
        lines.insert(insert_index, line + '\n')
        insert_index += 1

    with codecs.open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(lines)

    print(f"✅ Updated: {file_path}")

def traverse_lib(lib_path):
    for root, _, files in os.walk(lib_path):
        for file in files:
            if file.endswith('.dart'):
                full_path = os.path.join(root, file)
                process_file(full_path)

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("❗️请提供 lib 路径，例如： python3 inject_import.py ./lib")
        sys.exit(1)

    lib_dir = sys.argv[1]
    if not os.path.exists(lib_dir):
        print(f"❌ 路径不存在: {lib_dir}")
        sys.exit(1)

    traverse_lib(lib_dir)

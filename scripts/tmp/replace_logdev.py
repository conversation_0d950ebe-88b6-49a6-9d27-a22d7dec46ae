import re
import sys
from pathlib import Path

# 执行示例
# python3 replace_logdev.py /your/project/path


# 匹配 logDev(...)，支持跨行匹配参数
pattern = re.compile(r"logDev\s*\((.*?)\)", re.DOTALL)

# 提取并转换 logDev 为 LogX 调用
def extract_log_call(match: re.Match) -> str:
    args_str = match.group(1)

    # 判断日志类型
    if "error: true" in args_str:
        log_func = "LogE"
    elif "success: true" in args_str or "info: true" in args_str:
        log_func = "LogI"
    elif "special: true" in args_str:
        log_func = "LogF"
    elif any(flag in args_str for flag in ["alien: true", "ghost: true", "api: true"]):
        log_func = "LogW"
    else:
        log_func = "LogD"

    # 抓取第一个非命名参数作为消息
    lines = [line.strip() for line in args_str.split(",")]
    first_arg = lines[0] if lines else "''"

    return f"{log_func}({first_arg});"

# 处理单个 .dart 文件
def process_file(file: Path) -> int:
    if not file.is_file():
        return 0
    try:
        content = file.read_text(encoding="utf-8")
        new_content, count = pattern.subn(extract_log_call, content)
        if count > 0:
            file.write_text(new_content, encoding="utf-8")
        return count
    except Exception as e:
        print(f"❌ 无法处理文件 {file}: {e}")
        return 0

# 主入口
def main():
    if len(sys.argv) < 2:
        print("❗ 用法: python3 replace_logdev.py <项目路径>")
        sys.exit(1)

    project_root = Path(sys.argv[1])
    if not project_root.exists():
        print(f"❌ 路径不存在: {project_root}")
        sys.exit(1)

    dart_files = project_root.rglob("*.dart")
    total_replaced = 0

    for file in dart_files:
        replaced = process_file(file)
        if replaced > 0:
            print(f"✅ 替换 {replaced} 次: {file}")
            total_replaced += replaced

    print(f"\n🎉 全部替换完成，总计替换 {total_replaced} 次。")

if __name__ == "__main__":
    main()
#!/bin/bash

# 回滚临时替换的资源文件（基于切换脚本 switch_flavor.sh）

if [ -z "$1" ]; then
  echo "用法: ./rollback_flavor.sh [flavor]"
  exit 1
fi

channel="_$1"
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)

TARGET_DIR="$PROJECT_ROOT/assets"
TRANSLATIONS_DIR="$TARGET_DIR/translations"
SKINS_DIR="$TARGET_DIR/skins"

# 回滚单个文件
rollback_file() {
  local file="$1"
  if git ls-files --error-unmatch "$file" >/dev/null 2>&1; then
    echo "回滚：$file"
    git checkout "$file"
  fi
}

# 回滚翻译文件
echo ">>>>> 回滚翻译文件"
for lang_file in "en-US.json" "zh-CN.json" "zh-TW.json"; do
  rollback_file "$TRANSLATIONS_DIR/$lang_file"
done

# 回滚 flavor 替换的 assets 下的文件（排除 translations）
echo ">>>>> 回滚 flavor assets 文件"
find "$TARGET_DIR" \( -path "$TARGET_DIR/translations" -prune \) -o \( -type f -print \) | while read -r file; do
  flavor_path="$PROJECT_ROOT/assets/flavors/$1"
  if grep -q "$flavor_path" <<< "$file"; then
    continue
  fi

  rollback_file "$file"
done

# 回滚皮肤资源（icons / images / svg）
echo ">>>>> 回滚皮肤资源文件"
for folder in icons images svg; do
  find "$TARGET_DIR/$folder" -type f 2>/dev/null | while read -r file; do
    rollback_file "$file"
  done
done

echo
echo "✅ 所有资源已回滚完毕。"

#!/bin/bash -ilex

# 获取当前时间
current_time=$(date +"%Y-%m-%d %H:%M:%S")

#获取Git SHA1
GIT_SHA1=`(git show-ref --head --hash=8 2> /dev/null || echo 00000000) | head -n1`


#打包类型
if [ -z "$environment" ]; then
  echo "Error: 未设置发布环境>environment"
  exit 1
else
  type=$environment
fi

#设置flavor
if [ -z "$flavor" ]; then
  echo "Error: 未设置flavor"
  exit 1
else
  FLAVOR_LOWER=$(echo "$flavor" | tr '[:upper:]' '[:lower:]')
fi

# 设置目标路径
TARGET_PATH="/usr/local/var/www/${FLAVOR_LOWER}_${type}"
echo "👉目标路径：$TARGET_PATH"

# 如果目录不存在，则创建
if [ ! -d "$TARGET_PATH" ]; then
  echo "📁 目录不存在，正在创建..."
  mkdir -p "$TARGET_PATH"
  mkdir -p "$TARGET_PATH/assets"
fi

# ------- 处理 APK -------
APK_PATH=$(find "$HOME/Desktop/Archive/$flavor/$type" -maxdepth 1 -name "*.apk" | head -n 1)

if [[ -z "$APK_PATH" || ! -f "$APK_PATH" ]]; then
  echo "⚠️ 找不到 APK 文件，请检查路径 $HOME/Desktop/Archive/$flavor/$type"
else
  echo "找到 APK 文件: $APK_PATH"
  rm -f "$TARGET_PATH"/*.apk
  cp -f "$APK_PATH" "$TARGET_PATH"
  APK_FILENAME=$(basename "$APK_PATH")
  FINAL_APK_PATH="$TARGET_PATH/$APK_FILENAME"
  echo "✅ 已复制 APK 到: $FINAL_APK_PATH"
fi

# 移动logo至静态文件夹目录
cp -f assets/images/logo/app_logo.png "$TARGET_PATH"/assets/logo.png
if [[ -f "$TARGET_PATH/assets/logo.png" ]]; then
  echo "✅ logo.png 移动成功"
else
  echo "❌ logo.png 移动失败"
fi

# 移动下载按钮静态资源
cp -f scripts/internal_testing_page/asset/* "$TARGET_PATH"/assets/
if compgen -G "$TARGET_PATH/assets/*.png" > /dev/null; then
  echo "✅ 下载按钮资源已移动"
else
  echo "❌ 下载按钮资源移动失败"
fi

#获取版本号
pubspecFile="pubspec.yaml"
versionLine=$(grep -E "^[[:space:]]*version:[[:space:]]+[0-9]" "$pubspecFile" | head -1)

if [[ -z "$versionLine" ]]; then
  echo "❌ 未找到合法版本字段"
  exit 1
fi

versionString=$(echo "$versionLine" | sed 's/^[[:space:]]*version:[[:space:]]*//')
versionName=${versionString%%+*}
versionCode=${versionString##*+}

#指定版本号
if [ ! $buildVersionName ]; then
  echo "没有指定版本，默认使用配置文件的版本"
else
  versionName=$buildVersionName
fi

echo "[BUILD_VERSION] $versionString"

# 获取本机 IP 地址作为 base_url
IP=$(ipconfig getifaddr en0 || ipconfig getifaddr en1)
echo "本机 IP 地址: $IP"

BASE_URL="https://${IP}/app/${flavor}/${type}"
echo "✅ BASE_URL=$BASE_URL"
echo "[BUILD_OUTPUT_URL] $BASE_URL"

# 查找apk和ipa
apk_file=$(find "$TARGET_PATH" -maxdepth 1 -name "*.apk" | head -n 1)
ipa_file=$(find "$TARGET_PATH" -maxdepth 1 -name "*.ipa" | head -n 1)

# 提取相对路径 (去掉/usr/local/var/www/)
apk_relative_path=${apk_file#${TARGET_PATH}/}
ipa_relative_path=${ipa_file#${TARGET_PATH}/}

# 组合URL
ANDROID_URL="${BASE_URL}/${apk_relative_path}"
IOS_URL="${BASE_URL}/${ipa_relative_path}"
IOS_MANIFEST_URL="${BASE_URL}/manifest.plist"

# 根据 flavor 设置 APP_NAME
case "$flavor" in
  "gp")
    APP_NAME="GP Stock"
    ;;
  "rsyp")
    APP_NAME="荣顺优配"
    ;;
  "pre")
    APP_NAME="GP PRE"
    ;;
  "yhxt")
    APP_NAME="沅和信投"
    ;;
  "tempa")
    APP_NAME="TEMPA"
    ;;
  "bszb")
    APP_NAME="宝石资本"
    ;;
  *)
    echo "未知的 flavor: $flavor"
    exit 1
    ;;
esac

echo "<!doctype html>
<!DOCTYPE html>
<html lang='zh-CN'>

<head>
  <meta charset='UTF-8' />
  <meta name='viewport' content='width=device-width, initial-scale=1.0'>
  <title>$APP_NAME 下载中心</title>
  <style>
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, #e0f2ff, #ffffff);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
      text-align: center;
    }

    h1 {
      font-size: 26px;
      color: #333;
      margin-bottom: 10px;
    }

    .logo {
      width: 50px;
      height: 50px;
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      margin-bottom: 20px;
    }

    .info-block {
      /* width: 90%; */
      max-width: 400px;
      margin-bottom: 20px;
      text-align: left;
    }

    .info-block-container {
      display: flex;
      justify-content: center;
    }

    .info {
      font-size: 14px;
      color: #666;
      margin: 4px 0;
    }

    @media (max-width: 440px) {
      .buttons {
        flex-direction: column;
        align-items: center;
      }

      .button {
        width: 90vw;
        max-width: 300px;
        padding-left: 80px;
        justify-content: center;
      }
    }

    .buttons {
      display: flex;
      gap: 20px;
      margin-bottom: 30px;
    }

    .button {
      width: 200px;
      height: 64px;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: 90px;
      padding-right: 35px;
      color: white;
      font-size: 16px;
      font-weight: bold;
      border-radius: 12px;
      text-decoration: none;
      box-sizing: border-box;
      background-color: transparent;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
      transition: transform 0.2s ease;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }

    .button:hover {
      transform: scale(1.03);
    }

    .android {
      background-image: url('assets/btn_android.png');
    }

    .ios {
      background-image: url('assets/btn_ios.png');
    }

    .qrcode {
      margin-top: 20px;
    }

    .qrcode img {
      width: 140px;
      height: 140px;
    }

    .qrcode p {
      margin-top: 10px;
      font-size: 13px;
      color: #555;
    }
  </style>
  <script>
    function detectDevice() {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      const androidButton = document.getElementById('android-download');
      const iosButton = document.getElementById('ios-download');
      const iosLink = document.getElementById('ios-link');
      const certLink = document.getElementById('cert-link');

      if (/android/i.test(userAgent)) {
        androidButton.style.display = 'flex';
        iosButton.style.display = 'none';
        certLink.style.display = 'none';
      } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        androidButton.style.display = 'none';
        iosButton.style.display = 'flex';
        iosLink.href = 'itms-services://?action=download-manifest&url=$IOS_MANIFEST_URL';
        certLink.style.display = 'flex';
      } else {
        androidButton.style.display = 'flex';
        iosButton.style.display = 'flex';
        iosLink.href = '$IOS_URL'; // 直接指向ipa文件
        certLink.style.display = 'none';
      }
    }

    window.onload = function() {
      detectDevice();
      const qrcode = document.getElementById('qrcode');
      const currentURL = window.location.href;
      qrcode.src = 'https://api.qrserver.com/v1/create-qr-code/?data=' + encodeURIComponent(currentURL) + '&size=140x140';
    };
  </script>
</head>

<body>
  <img src='assets/logo.png' alt='Logo' class='logo'>
  <h1>欢迎下载 ${APP_NAME}</h1>
  <div class='info-block'>
    <div class='info'>环境：${type}</div>
    <div class='info'>版本：${versionName}_b${versionCode}</div>
    <div class='info'>更新时间：${current_time}</div>
  </div>
  <div style='height: 20px;'></div> 
  <div class='buttons'>
    <a id='android-download' class='button android' href='$ANDROID_URL' download style='display: none;'>安卓下载</a>
    <div id='ios-download' style='display: none; flex-direction: column; align-items: center;'>
      <a id='ios-link' class='button ios' href='itms-services://?action=download-manifest&url=$IOS_URL'>苹果下载</a>
      <div id='cert-link' style='margin-top: 10px; display: none;'>
        <a href='/rootCA.crt' onclick=\"alert('请在安装后操作：1.前往 设置 → 通用 → VPN与设备管理 → 安装描述文件；2.前往 设置 → 关于本机 → 证书信任设置 → 启用"完全信任 rootCA"');\"
          style='font-size: 14px; color: #007aff; text-decoration: none;'>
          📥 安装证书(首次下载必须*)
        </a>
      </div>
    </div>
  </div>

  <div class='qrcode'>
    <img id='qrcode' src='' alt='二维码'>
    <p>扫码下载 App</p>
  </div>
</body>

</html>
" > "$TARGET_PATH/index.html"

sudo chmod -R 755 "$TARGET_PATH"
sudo chown -R $(whoami):staff "$TARGET_PATH"

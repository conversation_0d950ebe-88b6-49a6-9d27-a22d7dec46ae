import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import 'package:gp_stock_app/features/notifications/domain/models/notification/notification_model.dart';

/// Handles all notification-related API calls
class NotificationsApi {
  /// Get notification list
  /// 获取通知列表
  static Future<ResponseModel<NotificationModel>> getNotificationList({
    required String messageType,
    required int page,
  }) async {
    return await Http().request<NotificationModel>(
      ApiEndpoints.notificationPage,
      method: HttpMethod.get,
      queryParameters: {
        'type': messageType,
        'pageNumber': page,
        'pageSize': 20,
      },
    );
  }

  /// Get notification count
  /// 获取通知数量
  static Future<ResponseModel<int>> getNotificationCount() async {
    return await Http().request<int>(
      ApiEndpoints.notificationCount,
      method: HttpMethod.get,
    );
  }

  /// Mark notification as read
  /// 标记通知为已读
  static Future<ResponseModel<bool>> readNotification({
    required messageId,
  }) async {
    return await Http().request<bool>(
      ApiEndpoints.notificationRead,
      method: HttpMethod.post,
      params: {
        'messageId': messageId,
      },
    );
  }
}

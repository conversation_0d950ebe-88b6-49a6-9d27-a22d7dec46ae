import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/notifications/domain/models/notification/notification_model.dart';

/// Handles all notification-related API calls
class NotificationsApi {
  /// Get notification list
  /// 获取通知列表
  static Future<ResponseResult<NotificationModel>> getNotificationList({
    required String messageType,
    required int page,
  }) async {
    try {
      final response = await Http().request<NotificationModel>(
        ApiEndpoints.notificationPage,
        method: HttpMethod.get,
        queryParameters: {
          'type': messageType,
          'pageNumber': page,
          'pageSize': 20,
        },
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to get notification list');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  /// Get notification count
  /// 获取通知数量
  static Future<ResponseResult<int>> getNotificationCount() async {
    try {
      final response = await Http().request<int>(
        ApiEndpoints.notificationCount,
        method: HttpMethod.get,
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to get notification count');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  /// Mark notification as read
  /// 标记通知为已读
  static Future<ResponseResult<bool>> readNotification({
    required messageId,
  }) async {
    try {
      final response = await Http().request<bool>(
        ApiEndpoints.notificationRead,
        method: HttpMethod.post,
        params: {
          'messageId': messageId,
        },
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to mark notification as read');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}

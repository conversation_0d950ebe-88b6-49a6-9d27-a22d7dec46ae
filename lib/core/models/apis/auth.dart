import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/user.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class AuthApi {
  /// 获取是否需要网易行为验证
  static Future<bool> fetchWangYiCaptchaRequired({required WangYiCaptchaType type}) async {
    // sceneType(1.Login登陆 2.SMS短信）
    final res = await Http().request(
      ApiEndpoints.wangYiCaptcha,
      method: HttpMethod.get,
      queryParameters: {"sceneType": type.code},
      needSignIn: false,
    );
    return res.data == true;
  }

  /// 获取短信验证码
  static Future<bool> fetchOTP({
    required String phoneNumber,
    required String sendType,
    String? validate,
  }) async {
    final res = await Http().request<bool>(ApiEndpoints.sendMsg,
        needSignIn: false,
        params: {"mobile": phoneNumber, "sendType": sendType, if (validate != null) "validate": validate});
    return res.data == true;
  }

  /// 登录
  static Future<UserModel?> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  }) async {
    final res = await Http().request<UserModel>(ApiEndpoints.login,
        params: {
          "mobile": mobile,
          "password": password.toBase64(),
          "verifyType": mode.text,
          "smsCode": smsCode,
          if (validate != null) "validate": validate,
        },
        needSignIn: false);
    return res.data;
  }

  /// 登出
  static Future<bool> logout() async {
    try {
      final res = await Http().request<bool>(
        ApiEndpoints.logout,
        method: HttpMethod.post,
      );
      return res.isSuccess;
    } catch (e) {
      return false;
    }
  }

  /// 请求短信验证码
  static Future<bool> requestOTP({
    required String phoneNumber,
    required String sendType,
  }) async {
    try {
      return await fetchOTP(
        phoneNumber: phoneNumber,
        sendType: sendType,
      );
    } catch (e) {
      return false;
    }
  }

  /// 检查是否需要网易验证码
  static Future<bool> requestWangYiCaptchaRequired() async {
    try {
      return await fetchWangYiCaptchaRequired(type: WangYiCaptchaType.kLogin);
    } catch (e) {
      return false;
    }
  }

  /// 获取用户数据
  static Future<UserModel?> getUserInfo() async {
    final res = await Http().request<UserModel>(ApiEndpoints.getUserInfo, method: HttpMethod.get);
    return res.data;
  }
}

import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/company_news/domain/models/company_news_details.dart';

/// Handles all company news-related API calls
class CompanyNewsApi {
  /// Get company news details
  /// 获取公司新闻详情
  static Future<ResponseResult<CompanyNewsDetailsResponse>> getCompanyNewsDetails(String id) async {
    try {
      final response = await Http().request<CompanyNewsDetailsResponse>(
        ApiEndpoints.companyNewsInfo,
        method: HttpMethod.get,
        queryParameters: {'id': id},
        needSignIn: false,
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to get company news details');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}

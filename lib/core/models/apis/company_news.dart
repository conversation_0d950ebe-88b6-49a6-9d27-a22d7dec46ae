import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import 'package:gp_stock_app/features/company_news/domain/models/company_news_details.dart';

/// Handles all company news-related API calls
class CompanyNewsApi {
  /// Get company news details
  /// 获取公司新闻详情
  static Future<ResponseModel<CompanyNewsDetailsResponse>> getCompanyNewsDetails(String id) async {
    return await Http().request<CompanyNewsDetailsResponse>(
      ApiEndpoints.companyNewsInfo,
      method: HttpMethod.get,
      queryParameters: {'id': id},
      needSignIn: false,
    );
  }
}

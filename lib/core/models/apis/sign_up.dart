import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/services/http/http.dart';

import '../../../features/sign_up/domain/models/sign_up_request.dart';

class SignUpApi {
  /// User registration
  /// 用户注册
  static Future<ResponseResult<bool>> register({required SignUpRequest signUpRequest}) async {
    try {
      final response = await Http().request<bool>(
        ApiEndpoints.register,
        method: HttpMethod.post,
        params: signUpRequest.toJson(),
        needSignIn: false,
      );

      if (response.isSuccess) {
        return ResponseResult(data: true);
      } else {
        return ResponseResult(error: response.msg ?? 'Registration failed');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}

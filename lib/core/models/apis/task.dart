import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/activity/domain/models/reward/reward.dart';

/// 任务相关接口
class TaskApi {
  /// 获取所有任务
  static Future<TaskCenterResponseEntity?> fetchTaskCenterData() async {
    final res = await Http().request<TaskCenterResponseEntity>(
      ApiEndpoints.tasks,
      method: HttpMethod.get,
    );
    return res.data;
  }

  /// 收集奖励
  /// Collect reward
  static Future<RewardResponse?> collectReward(int taskId) async {
    final response = await Http().request<RewardResponse>(
      ApiEndpoints.collectReward,
      method: HttpMethod.post,
      queryParameters: {'taskId': taskId},
    );
    return response.data;
  }
}

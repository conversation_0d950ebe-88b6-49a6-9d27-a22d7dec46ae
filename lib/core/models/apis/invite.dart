import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import 'package:gp_stock_app/features/invite/domain/models/invite_detail_model.dart';

/// Handles all invite-related API calls
class InviteApi {
  /// Get invite details
  /// 获取邀请详情
  static Future<ResponseModel<InviteDetailModel>> getInviteDetails() async {
    return await Http().request<InviteDetailModel>(
      ApiEndpoints.inviteDetail,
      method: HttpMethod.get,
    );
  }
}

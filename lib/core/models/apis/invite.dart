import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/invite/domain/models/invite_detail_model.dart';

/// Handles all invite-related API calls
class InviteApi {
  /// Get invite details
  /// 获取邀请详情
  static Future<ResponseResult<InviteDetailModel>> getInviteDetails() async {
    try {
      final response = await Http().request<InviteDetailModel>(
        ApiEndpoints.inviteDetail,
        method: HttpMethod.get,
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to get invite details');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}

import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import 'package:gp_stock_app/features/activity/domain/models/reward/reward.dart';
import 'package:gp_stock_app/features/activity/domain/models/tasks/tasks.dart';

/// Handles all tasks and rewards-related API calls.
class TasksApi {
  /// Get tasks
  /// 获取任务列表
  static Future<ResponseModel<TasksResponse>> getTasks() async {
    return await Http().request<TasksResponse>(
      ApiEndpoints.tasks,
      method: HttpMethod.get,
    );
  }

  /// Collect reward
  /// 收集奖励
  static Future<ResponseModel<RewardResponse>> collectReward(int taskId) async {
    return await Http().request<RewardResponse>(
      ApiEndpoints.collectReward,
      method: HttpMethod.get,
      queryParameters: {'taskId': taskId},
    );
  }
}

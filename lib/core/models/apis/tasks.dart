import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/activity/domain/models/reward/reward.dart';
import 'package:gp_stock_app/features/activity/domain/models/tasks/tasks.dart';

/// Handles all tasks and rewards-related API calls.
class TasksApi {
  /// Get tasks
  /// 获取任务列表
  static Future<ResponseResult<TasksResponse>> getTasks() async {
    try {
      final response = await Http().request<TasksResponse>(
        ApiEndpoints.tasks,
        method: HttpMethod.get,
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to get tasks');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  /// Collect reward
  /// 收集奖励
  static Future<ResponseResult<RewardResponse>> collectReward(int taskId) async {
    try {
      final response = await Http().request<RewardResponse>(
        ApiEndpoints.collectReward,
        method: HttpMethod.get,
        queryParameters: {'taskId': taskId},
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to collect reward');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}

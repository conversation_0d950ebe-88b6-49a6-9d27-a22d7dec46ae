import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/convert_rate/domain/models/convert_rate_model.dart';

/// Handles all convert rate-related API calls
class ConvertRateApi {
  /// Get convert rates
  /// 获取汇率转换
  static Future<ResponseResult<List<ConvertRate>>> getConvertRates() async {
    try {
      final response = await Http().request<List<ConvertRate>>(
        ApiEndpoints.exchangeRate,
        method: HttpMethod.get,
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to get convert rates');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}

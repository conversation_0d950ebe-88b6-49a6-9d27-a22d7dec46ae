import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import 'package:gp_stock_app/features/convert_rate/domain/models/convert_rate_model.dart';

/// Handles all convert rate-related API calls
class ConvertRateApi {
  /// Get convert rates
  /// 获取汇率转换
  static Future<ResponseModel<List<ConvertRate>>> getConvertRates() async {
    return await Http().request<List<ConvertRate>>(
      ApiEndpoints.exchangeRate,
      method: HttpMethod.get,
    );
  }
}

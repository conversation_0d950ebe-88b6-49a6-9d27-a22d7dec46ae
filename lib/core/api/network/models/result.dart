// /// A generic class that represents the result of an API response.
// ///
// /// This class encapsulates the data returned from an API call, any error message
// /// that may have occurred, and an optional token for authentication or session management.
// ///
// /// [T] is a generic type parameter that allows this class to handle different types of data.
// ///
// /// ## Usage:
// ///
// /// To use the `ResponseResult` class, you can create an instance of it by passing
// /// the data, error message, and token as needed. You can also check if the response
// /// was successful by using the `isSuccess` property.
// ///
// /// Example:
// /// ```dart
// /// final result = ResponseResult<String>(data: "Success", token: "abc123");
// /// if (result.isSuccess) {
// ///   print("Data: ${result.data}");
// /// } else {
// ///   print("Error: ${result.error}");
// /// }
// /// ```
// class ResponseResult<T> {
//   final T? data;
//   final String? error;
//   final String? token;

//   ResponseResult({this.data, this.error, this.token});

//   /// Returns true if the response contains data, indicating a successful result.
//   bool get isSuccess => data != null;
// }

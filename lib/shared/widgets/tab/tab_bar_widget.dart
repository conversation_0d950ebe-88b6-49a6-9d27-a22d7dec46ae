import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';

class TabComponentBox extends StatelessWidget {
  final String label1;
  final String label2;
  final Function selectTab;
  final Widget? widget;
  const TabComponentBox({
    super.key,
    required this.label1,
    required this.label2,
    required this.selectTab,
    this.widget,
  });

  @override
  Widget build(BuildContext context) => Container(
        height: 25.gh,
        width: 180.gw,
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          border: Border.all(color: context.theme.primaryColor),
          borderRadius: BorderRadius.circular(2.0),
        ),
        child: BlocSelector<TradingCubit, TradingState, int>(
          selector: (state) => state.tradeType.index,
          builder: (context, selectedIndex) {
            return Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      selectTab(0);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      color: selectedIndex == 0 ? context.theme.primaryColor : Colors.transparent,
                      child: Text(
                        label1,
                        style: context.textTheme.primary.fs12.w600.copyWith(
                          color: selectedIndex == 0 ? Colors.white : context.theme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: InkWell(
                    onTap: () {
                      selectTab(1);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      color: selectedIndex == 1 ? context.theme.primaryColor : Colors.transparent,
                      child: Text(
                        label2,
                        style: context.textTheme.primary.fs12.w600.copyWith(
                          color: selectedIndex == 1 ? Colors.white : context.theme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      );
}

import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';

import '../../features/home/<USER>/sort_icon.dart';
import '../constants/enums.dart';

class SortHeader extends StatelessWidget {
  final String title;
  final SortType? sortType;
  final VoidCallback? onTap;
  final MainAxisAlignment alignment;
  final TextStyle textStyle;

  const SortHeader({
    super.key,
    required this.title,
    required this.sortType,
    required this.onTap,
    this.alignment = MainAxisAlignment.start,
    required this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Row(
        mainAxisAlignment: alignment,
        children: [
          Text(
            title,
            style: textStyle,
            textAlign: alignment == MainAxisAlignment.center ? TextAlign.center : TextAlign.start,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          4.horizontalSpace,
          SortIcon(sortValue: sortType),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ShadowBox extends StatelessWidget {
  const ShadowBox({super.key, required this.child, this.padding, this.borderRadius});

  final Widget child;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: borderRadius ?? BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: context.theme.shadowColor,
            blurRadius: 8, // Soft blur effect
            spreadRadius: 0,
            offset: Offset(0, 4), // Downward shadow
          ),
        ],
      ),
      child: child,
    );
  }
}

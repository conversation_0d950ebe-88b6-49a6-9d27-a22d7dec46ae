import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';

import 'package:url_launcher/url_launcher.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class SupportWidget extends StatelessWidget {
  const SupportWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          text: '${'encounterAnyProblem'.tr()} ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[500],
          ),
          children: [
            TextSpan(
              text: 'pleaseContactCustomerService'.tr(),
              style: TextStyle(
                fontSize: 11.gsp,
                color: context.theme.primaryColor,
                fontWeight: FontWeight.bold,
              ),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  // Verify authentication before opening the service URL
                  context.verifyAuth(() {
                    // Get the service URL from system settings
                    final state = context.read<SysSettingsCubit>().state;

                    // Use pattern matching with maybeWhen to handle different states
                    state.maybeWhen(
                      loaded: (sysSettings, _) {
                        final serviceUrl = sysSettings.service;
                        if (serviceUrl != null && serviceUrl.isNotEmpty) {
                          launchUrl(Uri.parse(serviceUrl), mode: LaunchMode.inAppBrowserView);
                        } else {
                          GPEasyLoading.showToast('something_went_wrong'.tr());
                        }
                      },
                      orElse: () {
                        // Fallback if system settings are not loaded
                        GPEasyLoading.showToast('something_went_wrong'.tr());
                      },
                    );
                  });
                },
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';

class CustomNumberPicker<T> extends StatefulWidget {
  /// The list of selectable numbers
  final List<T> numbers;

  /// Initial selected value
  final T? initialValue;

  /// Callback when a number is selected
  final ValueChanged<T>? onSelectedNumberChanged;

  /// Optional title for the picker
  final String? title;

  /// Optional item builder for customizing the picker items
  final Widget Function(T) itemBuilder;

  const CustomNumberPicker({
    super.key,
    required this.numbers,
    this.initialValue,
    this.onSelectedNumberChanged,
    this.title,
    required this.itemBuilder,
  });

  @override
  CustomNumberPickerState createState() => CustomNumberPickerState();

  /// Show the picker as a bottom sheet
  static Future<T?> show<T>(
    BuildContext context, {
    required List<T> numbers,
    T? initialValue,
    String? title,
    ValueChanged<T?>? onSelectedNumberChanged,
    required Widget Function(T) itemBuilder,
  }) {
    if (numbers.isEmpty) {
      return Future<T?>.value(null);
    }
    return showModalBottomSheet<T>(
      context: context,
      builder: (BuildContext context) {
        return CustomNumberPicker(
          numbers: numbers,
          initialValue: initialValue,
          onSelectedNumberChanged: onSelectedNumberChanged,
          title: title,
          itemBuilder: itemBuilder,
        );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
    );
  }
}

class CustomNumberPickerState<T> extends State<CustomNumberPicker<T>> {
  late FixedExtentScrollController _scrollController;
  late T _selectedNumber;

  @override
  void initState() {
    super.initState();
    // Set initial value or default to first number in the list
    _selectedNumber = widget.initialValue ?? widget.numbers.first;

    // Find the index of the initial value
    final initialIndex = widget.numbers.indexOf(_selectedNumber);

    // Create scroll controller with initial position
    _scrollController = FixedExtentScrollController(
      initialItem: initialIndex != -1 ? initialIndex : 0,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300.gh,
      color: context.theme.cardColor,
      child: Column(
        children: [
          // Header with Cancel and Confirm buttons
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0.gr, vertical: 12.0.gr),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () => Navigator.of(context).pop(),
                  child: Text(
                    'cancel'.tr(),
                    style: context.textTheme.primary,
                  ),
                ),
                if (widget.title != null)
                  Text(
                    widget.title!,
                    style: context.textTheme.primary.fs16.w600,
                  ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop(_selectedNumber);
                  },
                  child: Text(
                    'confirm'.tr(),
                    style: context.textTheme.primary,
                  ),
                ),
              ],
            ),
          ),

          // Picker
          Expanded(
            child: CupertinoPicker(
              scrollController: _scrollController,
              magnification: 1,
              squeeze: 1.2,
              useMagnifier: true,
              itemExtent: 50.0,
              onSelectedItemChanged: (int index) {
                setState(() {
                  _selectedNumber = widget.numbers[index];
                });
              },
              children: widget.numbers.map((number) {
                return widget.itemBuilder(number);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}

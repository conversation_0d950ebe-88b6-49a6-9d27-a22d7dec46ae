import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class DialogHeader extends StatelessWidget {
  final String title;
  const DialogHeader({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 60.gh,
          width: MediaQuery.of(context).size.width,
          child: Padding(
            padding: EdgeInsets.only(left: 20.gw, right: 5.gw),
            child: Row(
              children: [
                Expanded(
                    child: Text(
                  title,
                  style: context.textTheme.primary.fs18.w500,
                )),
                IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: const Icon(
                    Icons.cancel,
                    color: Color(0xffBFBFBF),
                    size: 26,
                  ),
                )
              ],
            ),
          ),
        ),
        const Divider(
          height: 0,
        )
      ],
    );
  }
}

class DialogFooter extends StatelessWidget {
  final String? title;
  final VoidCallback? onPressed;
  const DialogFooter({
    super.key,
    this.title,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: context.theme.cardColor,
      width: MediaQuery.of(context).size.width,
      child: TextButton(
        onPressed: onPressed ?? () => Navigator.pop(context),
        child: Text(
          title ?? ('cancel'.tr()),
          style: context.textTheme.primary.fs16,
        ),
      ),
    );
  }
}

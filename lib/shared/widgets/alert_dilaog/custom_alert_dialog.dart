import 'dart:async';
import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';

class CustomAlertDialog extends StatefulWidget {
  const CustomAlertDialog({
    super.key,
    this.child,
    this.width,
    this.height,
    this.title,
    this.message,
    this.messageTextStyle,
    this.titleTextStyle,
    this.actionButtonText,
    this.insetPadding,
    this.enableCloseBtn = true,
    this.onActionButtonPressed,
    this.isLoading,
    this.headerImage,
    this.buttonBackGroundColor,
    this.hideActionButton = false,
  });

  final Widget? child;
  final double? width;
  final double? height;
  final EdgeInsets? insetPadding;
  final String? title;
  final String? message;
  final TextStyle? messageTextStyle;
  final TextStyle? titleTextStyle;
  final String? actionButtonText;
  final bool enableCloseBtn;
  final Function()? onActionButtonPressed;
  final bool? isLoading;
  final String? headerImage;
  final Color? buttonBackGroundColor;
  final bool hideActionButton;

  @override
  State<CustomAlertDialog> createState() => _CustomAlertDialogState();
}

class _CustomAlertDialogState extends State<CustomAlertDialog> {
  Timer? debounceTimer;

  @override
  void dispose() {
    debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final buttonBackGroundColor = widget.buttonBackGroundColor ?? context.theme.primaryColor;
    return Material(
      color: Colors.transparent,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4.gh),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: <Widget>[
                BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 5,
                    sigmaY: 5,
                  ),
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
                Container(
                  width: widget.width,
                  padding: EdgeInsets.only(
                    left: 20.gh,
                    top: widget.child == null ? 65.gh : 43.gh,
                    right: 20.gh,
                    bottom: 20.gh,
                  ),
                  margin: widget.child == null ? EdgeInsets.only(top: 45.gh) : EdgeInsets.only(top: 0.gh),
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    color: context.theme.cardColor,
                    borderRadius: BorderRadius.circular(20.gh),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        offset: const Offset(0, 10),
                        blurRadius: 10.gh,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (widget.child == null)
                        Column(
                          children: [
                            if (widget.headerImage != null) 55.verticalSpace,
                            widget.title != null
                                ? Text(
                                    widget.title ?? '',
                                    style: widget.titleTextStyle ?? context.textTheme.primary.fs18.w500,
                                  )
                                : const SizedBox.shrink(),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 19.gh),
                              child: Text(
                                widget.message ?? '',
                                textAlign: TextAlign.center,
                                style: widget.messageTextStyle ?? context.textTheme.primary.fs16.w500,
                              ),
                            ),
                          ],
                        ),
                      widget.child ?? const SizedBox.shrink(),
                      if (!widget.hideActionButton) ...[
                        30.43.verticalSpace,
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 29.gh),
                          child: CustomMaterialButton(
                            isLoading: widget.isLoading ?? false,
                            onPressed: widget.onActionButtonPressed ??
                                () {
                                  if (debounceTimer != null) {
                                    debounceTimer!.cancel();
                                  }
                                  debounceTimer = Timer(const Duration(milliseconds: 250), () async {
                                    Navigator.pop(context);
                                  });
                                },
                            buttonText: widget.actionButtonText.toString(),
                            textStyle: context.textTheme.primary.fs16.w500,
                            width: 350.gh,
                            height: 56.gh,
                            color: buttonBackGroundColor,
                            borderColor: buttonBackGroundColor,
                          ),
                        ),
                      ]
                    ],
                  ),
                ),
                Positioned(
                  left: 20.gh,
                  right: 20.gh,
                  child: widget.headerImage != null
                      ? CircleAvatar(
                          backgroundColor: Colors.transparent,
                          radius: 60.gr,
                          child: SvgPicture.asset(widget.headerImage!),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class AlertDialogWithTextField extends StatelessWidget {
  const AlertDialogWithTextField({
    super.key,
    this.child,
    this.width,
    this.height,
    this.title,
    this.message,
    this.messageTextStyle,
    this.titleTextStyle,
    this.actionButtonText,
    this.insetPadding,
    this.enableCancelBtn = true,
    this.onConfirmButtonPressed,
    this.onCancelButtonPressed,
    this.isLoading,
    this.headerImage,
    this.buttonBackGroundColor = const Color(0xFF063b87),
  });

  final Widget? child;
  final double? width;
  final double? height;
  final EdgeInsets? insetPadding;
  final String? title;
  final String? message;
  final TextStyle? messageTextStyle;
  final TextStyle? titleTextStyle;
  final String? actionButtonText;
  final bool enableCancelBtn;
  final Function()? onConfirmButtonPressed;
  final Function()? onCancelButtonPressed;
  final bool? isLoading;
  final String? headerImage;
  final Color buttonBackGroundColor;

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.gr),
        ),
        elevation: 0,
        backgroundColor: Colors.transparent,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: <Widget>[
                BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 5,
                    sigmaY: 5,
                  ),
                  child: Container(
                    color: Colors.transparent,
                  ),
                ),
                Container(
                  width: width,
                  padding: EdgeInsets.only(
                    left: 20.gh,
                    top: child == null ? 65.gh : 43.gh,
                    right: 20.gh,
                    bottom: 20.gh,
                  ),
                  margin: child == null ? EdgeInsets.only(top: 45.gh) : EdgeInsets.only(top: 0.gh),
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20.gh),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        offset: const Offset(0, 10),
                        blurRadius: 10.gh,
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (child == null)
                        Column(
                          children: [
                            if (headerImage != null) 55.verticalSpace,
                            title != null
                                ? Padding(
                                    padding: const EdgeInsets.only(bottom: 20.0),
                                    child: Text(
                                      title ?? '',
                                      textAlign: TextAlign.center,
                                      style: titleTextStyle ?? context.textTheme.primary.fs18.w500,
                                    ),
                                  )
                                : const SizedBox.shrink(),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 19.gh),
                              child: Text(
                                message ?? '',
                                textAlign: TextAlign.center,
                                style: messageTextStyle ?? context.textTheme.primary.fs16.w500,
                              ),
                            ),
                          ],
                        ),
                      (child != null)
                          ? Column(
                              children: [
                                if (headerImage != null) 55.verticalSpace,
                                title != null
                                    ? Padding(
                                        padding: const EdgeInsets.only(
                                          bottom: 20.0,
                                        ),
                                        child: Text(
                                          title ?? '',
                                          textAlign: TextAlign.center,
                                          style: titleTextStyle ?? context.textTheme.primary.fs18.w500,
                                        ),
                                      )
                                    : const SizedBox.shrink(),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 19.gh),
                                  child: Text(
                                    message ?? '',
                                    textAlign: TextAlign.center,
                                    style: messageTextStyle ?? context.textTheme.primary.fs16.w500,
                                  ),
                                ),
                                child!,
                              ],
                            )
                          : const SizedBox.shrink(),
                      30.43.verticalSpace,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: CustomMaterialButton(
                              isLoading: isLoading ?? false,
                              onPressed: onCancelButtonPressed,
                              buttonText: 'cancel'.tr(),
                              textStyle: context.textTheme.primary.w600.copyWith(color: context.theme.primaryColor),
                              width: 108.gh,
                              height: 40.gh,
                              borderRadius: 6,
                              color: buttonBackGroundColor,
                            ),
                          ),
                          82.horizontalSpace,
                          Expanded(
                            child: CustomMaterialButton(
                              isLoading: isLoading ?? false,
                              onPressed: onConfirmButtonPressed,
                              buttonText: actionButtonText.toString(),
                              textStyle: context.textTheme.primary.w500,
                              width: 108.gh,
                              height: 40.gh,
                              borderRadius: 6,
                              color: buttonBackGroundColor,
                            ),
                          ),
                        ],
                      ),
                      30.verticalSpace,
                    ],
                  ),
                ),
                Positioned(
                  left: 20.gh,
                  right: 20.gh,
                  child: headerImage != null
                      ? CircleAvatar(
                          backgroundColor: Colors.transparent,
                          radius: 60.gr,
                          child: SvgPicture.asset(headerImage!),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

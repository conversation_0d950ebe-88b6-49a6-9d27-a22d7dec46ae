import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class ErrorRetryWidget extends StatelessWidget {
  /// A reusable error widget with a retry button
  const ErrorRetryWidget({
    super.key,
    this.errorMessage,
    this.onRetry,
    this.icon,
    this.retryText,
    this.showIcon = true,
  });

  /// The error message to display
  final String? errorMessage;

  /// Callback function when retry button is pressed
  final VoidCallback? onRetry;

  /// Custom icon to display (defaults to error icon)
  final IconData? icon;

  /// Custom text for the retry button (defaults to "Try Again")
  final String? retryText;

  /// Whether to show the icon
  final bool showIcon;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24.gr),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showIcon) ...[
              Icon(
                icon ?? Icons.error_outline_rounded,
                size: 64.gr,
                color: context.colorTheme.textRegular,
              ),
              16.verticalSpace,
            ],
            Text(
              errorMessage ?? 'somethingWentWrong'.tr(),
              textAlign: TextAlign.center,
              style: context.textTheme.primary.w500.copyWith(
                color: context.colorTheme.textRegular,
              ),
            ),
            24.verticalSpace,
            if (onRetry != null)
              ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.theme.primaryColor,
                  foregroundColor: context.theme.cardColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.gw,
                    vertical: 12.gh,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.gr),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  retryText ?? 'tryAgain'.tr(),
                  style: context.textTheme.primary.w500.copyWith(
                    color: context.theme.cardColor,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

class DropDownTextFieldWidget<T> extends StatefulWidget {
  const DropDownTextFieldWidget({
    super.key,
    this.labelText,
    this.topLabelText,
    this.hintText,
    this.textStyle,
    this.hintStyle,
    this.isHint = false,
    this.textInputType,
    this.textCapitalization = TextCapitalization.none,
    this.inputFormatters,
    this.textDirection,
    this.maxLines,
    this.maxLength,
    this.counterText,
    this.hideCounterText = false,
    this.controller,
    this.borderColor,
    this.prefixIcon,
    this.suffixIcon,
    this.suffixIconConstraints,
    this.autoValidateMode,
    this.validator,
    this.errorStyle = true,
    this.errorText,
    this.focusNode,
    this.enabled,
    this.isDense = true,
    this.contentPadding = const EdgeInsets.all(10),
    this.constraints,
    this.readOnly,
    this.onSaved,
    this.onChanged,
    this.onTap,
    this.obscureText,
    this.floatingLabelBehavior,
    this.fillColor,
    this.errorColor,
    this.fontSize,
    this.hintSize,
    this.textAlign = TextAlign.start,
    this.fontColor,
    this.isRequired = false,
    this.dropdownItems,
    this.initialDropdownItem,
    this.onDropdownChanged,
  });

  final String? labelText;
  final String? topLabelText;
  final String? hintText;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final bool isHint;
  final TextInputType? textInputType;
  final TextCapitalization textCapitalization;
  final List<TextInputFormatter>? inputFormatters;
  final TextDirection? textDirection;
  final int? maxLines, maxLength;
  final String? counterText;
  final bool hideCounterText;
  final Color? borderColor;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final BoxConstraints? suffixIconConstraints;
  final TextEditingController? controller;
  final AutovalidateMode? autoValidateMode;
  final String? Function(T?)? validator;
  final FocusNode? focusNode;
  final bool? enabled;
  final bool? readOnly;
  final bool? isDense;
  final Function(T?)? onSaved;
  final Function(T?)? onChanged;
  final Function()? onTap;
  final BoxConstraints? constraints;
  final FloatingLabelBehavior? floatingLabelBehavior;
  final EdgeInsetsGeometry? contentPadding;
  final bool? obscureText;
  final bool errorStyle;
  final String? errorText;
  final Color? fontColor, fillColor, errorColor;
  final double? fontSize, hintSize;
  final TextAlign textAlign;
  final bool isRequired;
  final List<DropdownMenuItem<T>>? dropdownItems;
  final DropdownMenuItem<T>? initialDropdownItem;
  final Function(DropdownMenuItem<T>?)? onDropdownChanged;

  @override
  DropDownTextFieldWidgetState<T> createState() => DropDownTextFieldWidgetState<T>();
}

class DropDownTextFieldWidgetState<T> extends State<DropDownTextFieldWidget<T>> {
  late FocusNode _focusNode;
  T? _selectedValue;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_handleFocusChange);
    _selectedValue = widget.initialDropdownItem?.value;
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _handleFocusChange() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DropdownButtonFormField<T>(
          value: _selectedValue,
          decoration: InputDecoration(
            floatingLabelBehavior: widget.floatingLabelBehavior,
            counterText: widget.hideCounterText ? '' : widget.counterText,
            labelText: widget.isHint ? null : widget.labelText,
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: widget.borderColor ?? Colors.transparent),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: context.theme.primaryColor.withAlpha(128)),
            ),
            errorBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: context.colorTheme.stockRed.withAlpha(128)),
            ),
            focusedErrorBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: context.colorTheme.stockRed.withAlpha(128)),
            ),
            labelStyle: TextStyle(
              color: context.colorTheme.textRegular,
            ),
            fillColor: widget.fillColor,
            filled: false,
            prefixIcon: widget.prefixIcon == null
                ? null
                : Padding(
                    padding: EdgeInsets.symmetric(horizontal: 13.0.gr),
                    child: ColorFiltered(
                      colorFilter: ColorFilter.mode(
                        _focusNode.hasFocus
                            ? context.theme.primaryColor
                            : context.theme.primaryColor.withValues(alpha: 0.5),
                        BlendMode.modulate,
                      ),
                      child: widget.prefixIcon,
                    ),
                  ),
            suffixIcon: widget.suffixIcon,
            suffixIconConstraints: widget.suffixIconConstraints,
            hintText: widget.isHint ? widget.labelText : widget.hintText,
            hintStyle: widget.hintStyle ??
                TextStyle(
                    fontSize: widget.hintSize ?? 15.gsp, color: context.theme.primaryColor.withValues(alpha: 0.5)),
            isDense: widget.isDense,
            errorText: widget.errorText,
            errorStyle: widget.errorStyle
                ? TextStyle(color: widget.errorColor, fontSize: 14.gsp, height: 1)
                : const TextStyle(fontSize: 0.01),
            constraints: widget.constraints,
            contentPadding: EdgeInsets.only(
              left: 13.0.gr,
              right: 13.0.gr,
              top: _selectedValue != null ? 10.0.gr : 6.0.gr,
            ),
          ),
          items: widget.dropdownItems,
          onChanged: (T? newValue) {
            setState(() {
              _selectedValue = newValue;
            });
            final selectedItem = widget.dropdownItems?.firstWhere(
              (item) => item.value == newValue,
              orElse: () => DropdownMenuItem<T>(value: null, child: Container()),
            );
            widget.onDropdownChanged?.call(selectedItem);
          },
          validator: widget.validator,
          style: context.textTheme.regular.fs16.copyWith(color: widget.fontColor ?? context.theme.primaryColor),
          icon: Icon(
            Icons.arrow_drop_down,
            color: _focusNode.hasFocus ? context.theme.primaryColor : context.theme.primaryColor.withValues(alpha: 0.5),
          ),
        ),
      ],
    );
  }
}

import 'dart:async';

import 'package:flutter/material.dart';

/// A widget that automatically scrolls text horizontally
class AutoScrollingText extends StatefulWidget {
  /// The text to scroll
  final String text;

  /// The text style
  final TextStyle style;

  /// The scroll speed in pixels per second
  final double scrollSpeed;

  /// The pause duration at the beginning and end
  final Duration pauseDuration;

  /// Creates an auto-scrolling text widget
  const AutoScrollingText({
    required this.text,
    required this.style,
    this.scrollSpeed = 30.0,
    this.pauseDuration = const Duration(seconds: 1),
    super.key,
  });

  @override
  State<AutoScrollingText> createState() => _AutoScrollingTextState();
}

class _AutoScrollingTextState extends State<AutoScrollingText> with SingleTickerProviderStateMixin {
  late ScrollController _scrollController;
  Timer? _scrollTimer;
  double _textWidth = 0.0;
  final GlobalKey _textKey = GlobalKey();
  final GlobalKey _containerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    // Start scrolling after a short delay to allow the widget to render and measure
    Future.delayed(const Duration(milliseconds: 500), () {
      _calculateWidths();
    });
  }

  @override
  void dispose() {
    _scrollTimer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  /// Calculate the width of the text and container
  void _calculateWidths() {
    // Get the width of the text
    final RenderBox? textBox = _textKey.currentContext?.findRenderObject() as RenderBox?;
    final RenderBox? containerBox = _containerKey.currentContext?.findRenderObject() as RenderBox?;

    if (textBox != null && containerBox != null) {
      setState(() {
        _textWidth = textBox.size.width;
      });

      // Start scrolling once we have the measurements
      _startScrolling();
    }
  }

  void _startScrolling() {
    // Cancel any existing timer
    _scrollTimer?.cancel();

    // Only start scrolling if the text is wider than the container
    // if (_textWidth <= _containerWidth) {
    //   return; // No need to scroll
    // }

    // Create a new timer that scrolls the content
    _scrollTimer = Timer.periodic(const Duration(milliseconds: 16), (timer) {
      if (!_scrollController.hasClients) return;

      // Define the gap width between repeated text
      const gapWidth = 50.0;

      // Increment the scroll position
      final newPosition = _scrollController.offset + (widget.scrollSpeed / 60);

      if (newPosition >= _textWidth + gapWidth) {
        // If we've scrolled past the first text + gap, jump back to the beginning
        // This creates the illusion of continuous scrolling
        _scrollController.jumpTo(0);
      } else {
        // Continue scrolling
        _scrollController.jumpTo(newPosition);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      key: _containerKey,
      child: SingleChildScrollView(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        physics: const NeverScrollableScrollPhysics(), // Disable manual scrolling
        child: Row(
          children: [
            // The main text
            Text(
              widget.text,
              key: _textKey,
              style: widget.style,
            ),
            // Gap between repeated text
            const SizedBox(width: 50.0), // Same as gapWidth in _startScrolling
            // Duplicate text to create continuous scrolling effect
            Text(
              widget.text,
              style: widget.style,
            ),
          ],
        ),
      ),
    );
  }
}

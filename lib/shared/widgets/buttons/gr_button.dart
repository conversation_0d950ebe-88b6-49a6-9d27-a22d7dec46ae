import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class GrButton extends StatelessWidget {
  final String text;
  final VoidCallback? onTap;
  final IconData? icon;
  final Color? color;
  final double? width;
  final double? height;

  const GrButton({
    super.key,
    required this.text,
    this.icon,
    this.onTap,
    this.color,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: onTap ?? () {},
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color?.withNewOpacity(0.6) ?? context.theme.primaryColor,
              color ?? context.theme.primaryColor,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withNewOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(2, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: Colors.white,
              ),
              const SizedBox(width: 8),
            ],
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class IcButton extends StatelessWidget {
  final VoidCallback? onTap;
  final IconData icon;
  final Color? color;

  const IcButton({
    super.key,
    required this.icon,
    this.onTap,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: onTap ?? () {},
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color?.withNewOpacity(0.6) ?? const Color.fromARGB(255, 240, 240, 240),
              color ?? const Color.fromARGB(255, 240, 240, 240),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withNewOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(2, 2),
            ),
          ],
        ),
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 6),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(2),
            child: Icon(
              icon,
              color: color ?? context.theme.primaryColor,
              size: 15,
            ),
          ),
        ),
      ),
    );
  }
}

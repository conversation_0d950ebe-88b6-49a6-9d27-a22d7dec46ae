import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class CustomRadioButton extends StatefulWidget {
  final double? size;
  final Function(bool) onChange;
  final Color? backgroundColor;
  final Color? iconColor;
  final Color? borderColor;
  final IconData? icon;
  final bool isSelected;
  final BoxShape shape;

  const CustomRadioButton({
    super.key,
    this.size,
    required this.onChange,
    this.backgroundColor,
    this.iconColor,
    this.icon,
    this.borderColor,
    required this.isSelected,
    this.shape = BoxShape.circle,
  });

  @override
  State<CustomRadioButton> createState() => _CustomRadioButtonState();
}

class _CustomRadioButtonState extends State<CustomRadioButton> {
  @override
  Widget build(BuildContext context) {
    final containerSize = widget.size ?? 14.0;

    return GestureDetector(
      onTap: () {
        widget.onChange(!widget.isSelected); // Toggle selection
      },
      child: Container(
        alignment: Alignment.center,
        child: AnimatedContainer(
          height: containerSize,
          width: containerSize,
          duration: const Duration(milliseconds: 500),
          curve: Curves.fastLinearToSlowEaseIn,
          decoration: BoxDecoration(
            shape: widget.shape,
            borderRadius: widget.shape == BoxShape.rectangle ? BorderRadius.circular(3) : null,
            color: widget.isSelected ? widget.backgroundColor ?? context.theme.primaryColor : Colors.transparent,
            border: Border.all(
              width: widget.shape == BoxShape.rectangle ? 1.5 : 1,
              color: widget.isSelected ? Colors.transparent : widget.borderColor ?? context.theme.primaryColor,
            ),
          ),
          child: widget.isSelected
              ? Icon(
                  widget.icon ?? Icons.check,
                  size: containerSize * 0.7, // Scale icon size relative to container
                  color: widget.iconColor ?? context.colorTheme.buttonPrimary,
                )
              : null,
        ),
      ),
    );
  }
}

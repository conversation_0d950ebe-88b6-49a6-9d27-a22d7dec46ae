import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_orders.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';

import '../../../core/utils/convert_helper.dart';
import '../../../features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart'; 

class TradeDetailsRow extends StatelessWidget {
  const TradeDetailsRow({
    super.key,
    required this.data,
    required this.contract,
  });

  final OrderRecord data;
  final ContractSummaryData? contract;
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TradingCubit(),
      child: Builder(builder: (context) {
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 6.gw,
          ),
          child: GestureDetector(
            onTap: () {
              context.read<TradingCubit>().getKlineDetailList(data.instrument, KlineConstants.options[0]);
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                builder: (_) {
                  return MultiBlocProvider(
                    providers: [
                      BlocProvider.value(
                        value: context.read<TradingCubit>(),
                      ),
                      BlocProvider(
                        create: (_) => FTradeKLineCubit(FTradeKLineScrollRepository()),
                      ),
                    ],
                    child: Builder(builder: (innerContext) {
                      if (data.isCnFTrade) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          innerContext
                              .read<FTradeKLineCubit>()
                              .fetchTimeLineSubData(instrument: data.instrument, period: 'day');
                        });
                      }
                      return TradeBottomsheetOrders(data: data, isTradeDetails: true, isTrading: false);
                    }),
                  );
                },
              );
            },
            child: Container(
              color: Colors.transparent,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Name and Symbol Column
                    Expanded(
                      flex: 3,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 5.gh,
                        children: [
                          Text(
                            data.symbolName ?? 'N/A',
                            style: context.textTheme.regular.w600,
                          ),
                          Row(
                            spacing: 5.gh,
                            children: [
                              SymbolChip(
                                  name: data.market?.substring(0, 2) ?? 'N/A', chipColor: context.theme.primaryColor),
                              Text(
                                data.symbol ?? 'N/A',
                                style: context.textTheme.primary.fs12.w600.copyWith(
                                  color: context.colorTheme.textRegular,
                                  fontFamily: 'Akzidenz-Grotesk',
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Latest Price Column
                    Expanded(
                      flex: 2,
                      child: Column(
                        children: [
                          Text(
                            data.tradePrice?.toStringAsFixed(3) ?? 'N/A',
                            textAlign: TextAlign.center,
                            style: context.textTheme.regular.ffAkz,
                          ),
                          Text(
                            data.tradeNum?.toStringAsFixed(1) ?? 'N/A',
                            textAlign: TextAlign.center,
                            style: context.textTheme.regular.ffAkz,
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                        flex: 2,
                        child: AnimatedFlipCounter(
                          fractionDigits: 2,
                          decimalSeparator: '.',
                          thousandSeparator: ',',
                          textStyle: context.textTheme.primary.w700.ffAkz,
                          value: data.transactionAmount ?? 0.00,
                        )),
                    // Change Percentage Column
                    Expanded(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            '${TradeDirection.fromValue(data.direction ?? 0).translationKey.tr()}${TradeType.fromValue(data.tradeType ?? 0).translationKey.tr()}',
                            textAlign: TextAlign.center,
                            style: context.textTheme.regular.fs12.copyWith(
                                color: TradeDirection.getColor(context,
                                    tradeDirection: TradeDirection.fromValue(data.direction ?? 0))),
                          ),
                          if (data.dealTime != null)
                            Text(
                              ConvertHelper.formatDateTypeIn24Hour(data.dealTime ?? ''),
                              textAlign: TextAlign.center,
                              style: context.textTheme.regular.w600.copyWith(
                                fontSize: 8.gh,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }
}

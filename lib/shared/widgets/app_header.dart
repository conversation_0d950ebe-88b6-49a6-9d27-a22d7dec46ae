import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';

import '../../features/home/<USER>/settings_menu.dart';
import '../models/sys_settings_model/sys_settings_model.dart';

mixin AppHeaderMixin {
  Widget buildAppHeader(BuildContext context, {double height = 320, bool showHeaderImage = false}) {
    return BlocSelector<SysSettingsCubit, SysSettingsState, SysSettingsModel?>(
      selector: (state) => state.maybeWhen(
        loaded: (sysSettings, _) => sysSettings,
        orElse: () => null,
      ),
      builder: (context, sysSettings) {
        final darkMode = isDarkMode(context);
        final logoUrl = (darkMode ? sysSettings?.logoDark : sysSettings?.logoLight) ?? '';
        final slogan = sysSettings?.slogan?.currentLocaleText ?? '';
        return SizedBox(
          width: double.infinity,
          height: height,
          child: Stack(
            children: [
              // Background SVG
              Image.asset(
                Assets.loginHeader,
                width: 1.gsw,
                height: height,
                fit: BoxFit.cover,
              ),

              // App Logo and Web Icon
              Positioned(
                top: 50.gh,
                left: 0,
                right: 0,
                child: Stack(
                  // alignment: Alignment.center,
                  children: [
                    if (Navigator.of(context).canPop()) ...[
                      BackButton(),
                    ],
                    // Centered Logo
                    Center(
                      child: CachedNetworkImage(
                        imageUrl: logoUrl,
                        fit: BoxFit.contain,
                        height: 32.gh,
                        errorWidget: (context, url, error) => SizedBox(height: 32, width: 32),
                      ),
                    ),

                    // Web Icon positioned on the right
                    Positioned(
                      right: 14.gw,
                      child: IconButton(
                        onPressed: () {
                          _showSubMenu(context: context, child: LanguageOptions());
                        },
                        icon: IconHelper.loadAsset(
                          Assets.webIcon,
                          width: 24.gw,
                          height: 24.gh,
                          color: switch (AppConfig.instance.skinStyle) {
                            AppSkinStyle.kTemplateD => context.colorTheme.textSecondary,
                            _ => context.colorTheme.textPrimary,
                          },
                        ),
                        iconSize: 24.gw,
                        padding: EdgeInsets.all(8.gw),
                      ),
                    ),
                  ],
                ),
              ),

              // Login Header and Image with Slogan
              if (showHeaderImage)
                Positioned(
                  bottom: switch (AppConfig.instance.skinStyle) {
                    AppSkinStyle.kGP => 80.gh,
                    _ => 200.gh,
                  },
                  left: 30.gw,
                  right: 0,
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (slogan.isNotEmpty)
                          Text(
                            slogan,
                            style: switch (AppConfig.instance.skinStyle) {
                              AppSkinStyle.kGP => context.textTheme.title.fs25.w500.copyWith(
                                  height: 1.3,
                                  fontFamily: 'PingFang-SC',
                                  color: Color(0xFF3C406A),
                                  fontSize: 30.gsp,
                                ),
                              AppSkinStyle.kTemplateD =>
                                  context.textTheme.title.fs25.w500.copyWith(height: 1.3, fontFamily: 'PingFang-SC'),
                              _ => context.textTheme.secondary.fs20.w500.copyWith(
                                  height: 1.3,
                                  fontFamily: 'PingFang-SC',
                                ),
                            },
                            maxLines: 3,
                            textAlign: TextAlign.left,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    ),
                  ),
                )
            ],
          ),
        );
      },
    );
  }

  Future<void> _showSubMenu({
    required BuildContext context,
    required Widget child,
  }) async {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final buttonPosition = button.localToGlobal(Offset.zero);

    await showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        buttonPosition.dx,
        buttonPosition.dy + 80.gh,
        buttonPosition.dx - 1.gh,
        0,
      ),
      elevation: 0,
      color: Colors.transparent,
      constraints: BoxConstraints(maxWidth: 120.gw),
      items: [
        PopupMenuItem(
          enabled: false,
          padding: EdgeInsets.zero,
          child: Container(
            width: 160.gw,
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(12.gr),
              boxShadow: [
                BoxShadow(
                  color: context.theme.shadowColor,
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: child,
          ),
        ),
      ],
    );
  }
}

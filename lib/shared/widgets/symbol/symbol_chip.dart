import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class SymbolChip extends StatelessWidget {
  final String name;
  final Color? chipColor;
  const SymbolChip({super.key, required this.name, this.chipColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 15.gh,
      padding: EdgeInsets.symmetric(horizontal: 4.gh, vertical: 2.gh),
      decoration: BoxDecoration(
        color: chipColor ?? context.theme.primaryColor,
        borderRadius: BorderRadius.circular(2.gh),
      ),
      child: Text(marketName, style: context.textTheme.secondary.w600.copyWith(fontSize: 8.gsp)),
    );
  }

  String get marketName {
    return switch (name) {
      'SZSE' => 'SZ',
      'SSE' => 'SH',
      'HKEX' => 'HK',
      'US' => 'US',
      _ => name,
    };
  }
}

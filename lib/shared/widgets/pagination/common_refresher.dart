import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'loadiing_footer.dart';
import 'refresh_header.dart';

/// A common widget that provides pull-to-refresh and infinite scrolling functionality.
///
/// This widget wraps the [SmartRefresher] widget from the pull_to_refresh package
/// and provides a consistent configuration across the app.
///
/// Example usage:
/// ```dart
/// CommonRefresher(
///   enablePullDown: true,
///   enablePullUp: true,
///   controller: _refreshController,
///   onRefresh: () {
///     // Handle refresh
///   },
///   onLoading: () {
///     // Handle loading more
///   },
///   child: ListView.builder(
///     // ...
///   ),
/// )
/// ```
class CommonRefresher<T> extends StatefulWidget {
  /// Whether to enable pull up to load more functionality
  final bool enablePullUp;

  /// Whether to enable pull down to refresh functionality
  final bool enablePullDown;

  /// Callback function when pull down refresh is triggered
  final Function()? onRefresh;

  /// Callback function when pull up load more is triggered
  final Function()? onLoading;

  /// Controller for managing refresh states
  final RefreshController controller;

  /// Background color of the refresher widget
  final Color? bgColor;

  /// The main content widget to be wrapped
  final Widget child;

  const CommonRefresher({
    super.key,
    this.enablePullUp = false,
    this.enablePullDown = false,
    required this.onRefresh,
    this.onLoading,
    required this.controller,
    this.bgColor,
    required this.child,
  });

  @override
  State<CommonRefresher> createState() => _CommonRefresherState<T>();
}

class _CommonRefresherState<T> extends State<CommonRefresher> {
  @override
  void initState() {
    super.initState();
  }

  /// Configures the refresh widget with standard settings
  Widget _configuration(Widget list) {
    return RefreshConfiguration(
      headerTriggerDistance: 10,
      footerTriggerDistance: 200,
      enableBallisticLoad: true,
      headerBuilder: () => const RefreshHeader(),
      footerBuilder: () => const LoadingFooter(),
      child: Container(
        color: widget.bgColor ?? Theme.of(context).scaffoldBackgroundColor,
        child: SmartRefresher(
          physics: const BouncingScrollPhysics(),
          enablePullUp: widget.enablePullUp,
          enablePullDown: widget.enablePullDown,
          controller: widget.controller,
          onRefresh: widget.onRefresh ?? () {},
          onLoading: widget.onLoading ?? () {},
          child: list,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) => _configuration(widget.child);
}

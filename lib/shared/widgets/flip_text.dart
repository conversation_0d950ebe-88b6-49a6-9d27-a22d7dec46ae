import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/logic/selected_exchange_cubit/selected_exchange_cubit.dart';
import 'package:gp_stock_app/shared/models/exchange_rate/exchange_rate.dart';

class FlipText extends StatelessWidget {
  const FlipText(
    this.amount, {
    super.key,
    this.style,
    this.prefix,
    this.suffix,
    this.isCurrency = false,
    this.showCurrencyDropdown = false,
    this.dropdownIconColor,
    this.fractionDigits = 2,
    this.selectedExchangeCubit,
  });

  final double amount;
  final TextStyle? style;
  final String? prefix;
  final String? suffix;
  final bool isCurrency;
  final bool showCurrencyDropdown;
  final Color? dropdownIconColor;
  final int fractionDigits;
  final SelectedExchangeCubit? selectedExchangeCubit;
  @override
  Widget build(BuildContext context) {
    return BlocProvider<SelectedExchangeCubit>(
      create: (context) => selectedExchangeCubit ?? getIt<SelectedExchangeCubit>(),
      child: Builder(builder: (context) {
        return BlocBuilder<SelectedExchangeCubit, ExchangeRate>(
          builder: (context, state) {
            final selectedRate = context.read<SelectedExchangeCubit>().currentExchangeRate;
            final rates = context.read<ExchangeRateCubit>().getRates();
            final convertedAmount = isCurrency ? amount * selectedRate.rate : amount;
            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedFlipCounter(
                  duration: const Duration(milliseconds: 500),
                  thousandSeparator: ',',
                  fractionDigits: fractionDigits,
                  textStyle: style?.ffAkz.copyWith(
                        height: 1,
                      ) ??
                      context.textTheme.primary.w800.ffAkz.copyWith(
                        height: 1,
                      ),
                  prefix: prefix,
                  suffix: isCurrency && suffix == null
                      ? ' ${selectedRate.currencyTarget}'
                      : suffix != null
                          ? '$suffix'
                          : '',
                  value: convertedAmount,
                ),
                if (isCurrency && showCurrencyDropdown)
                  SizedBox(
                    width: 30.gw,
                    height: 30.gh,
                    child: PopupMenuButton<ExchangeRate>(
                      padding: EdgeInsets.zero,
                      position: PopupMenuPosition.under,
                      elevation: 1,
                      menuPadding: EdgeInsets.zero,
                      constraints: BoxConstraints(
                        maxWidth: 80.gh,
                      ),
                      icon: Icon(
                        Icons.arrow_drop_down,
                        color: dropdownIconColor ?? context.colorTheme.textRegular,
                      ),
                      itemBuilder: (context) => rates.map((ExchangeRate value) {
                        final isSelected = context.read<SelectedExchangeCubit>().currentExchangeRate == value;
                        return PopupMenuItem<ExchangeRate>(
                          value: value,
                          child: Center(
                            child: Text(
                              value.currencyTarget,
                              style: context.textTheme.primary.w500.copyWith(
                                color: isSelected ? context.theme.primaryColor : context.colorTheme.textRegular,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                      onSelected: (ExchangeRate value) {
                        context.read<SelectedExchangeCubit>().updateSelectedExchangeRate(value);
                      },
                    ),
                  ),
              ],
            );
          },
        );
      }),
    );
  }
}

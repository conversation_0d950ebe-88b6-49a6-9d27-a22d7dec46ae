import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AnimatedText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final Duration typingSpeed;
  final bool isLoading;
  final bool enableAnimation;

  const AnimatedText({
    super.key,
    required this.text,
    this.style,
    this.typingSpeed = const Duration(milliseconds: 50),
    this.isLoading = false,
    this.enableAnimation = true,
  });

  @override
  State<AnimatedText> createState() => _AnimatedTextState();
}

class _AnimatedTextState extends State<AnimatedText> with TickerProviderStateMixin {
  String _displayText = '';
  Timer? _timer;
  int _currentIndex = 0;
  bool _showCursor = true;
  Timer? _cursorTimer;
  final List<AnimationController> _loadingControllers = [];

  @override
  void initState() {
    super.initState();
    _startTypingAnimation();
    _startCursorAnimation();

    // Initialize loading controllers
    for (int i = 0; i < 3; i++) {
      final controller = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 600),
      )..repeat(reverse: true);
      _loadingControllers.add(controller);
    }
  }

  void _startTypingAnimation() {
    if (widget.isLoading) {
      setState(() {
        _displayText = '';
        _currentIndex = 0;
      });
      return;
    }

    if (!widget.enableAnimation) {
      setState(() {
        _displayText = widget.text;
        _currentIndex = widget.text.length;
      });
      return;
    }

    _timer?.cancel();
    _timer = Timer.periodic(widget.typingSpeed, (timer) {
      if (_currentIndex < widget.text.length) {
        setState(() {
          _displayText += widget.text[_currentIndex];
          _currentIndex++;
        });
      } else {
        _timer?.cancel();
      }
    });
  }

  void _startCursorAnimation() {
    _cursorTimer?.cancel();
    _cursorTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (mounted) {
        setState(() {
          _showCursor = !_showCursor;
        });
      }
    });
  }

  @override
  void didUpdateWidget(AnimatedText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text || oldWidget.isLoading != widget.isLoading) {
      _timer?.cancel();
      _currentIndex = 0;
      _displayText = '';
      _startTypingAnimation();
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _cursorTimer?.cancel();
    for (final controller in _loadingControllers) {
      controller.dispose();
    }
    _loadingControllers.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Text('aiThinking'.tr(), style: widget.style),
            const SizedBox(width: 4),
            _buildLoadingDots(context),
          ],
        ),
      );
    }

    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: _displayText,
            style: widget.style,
          ),
          if (_currentIndex < widget.text.length)
            TextSpan(
              text: _showCursor ? '|' : ' ',
              style: widget.style?.copyWith(color: Colors.blue),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingDots(BuildContext context) {
    return SizedBox(
      width: 24,
      child: Row(
        children: List.generate(3, (index) {
          return Expanded(
            child: FadeTransition(
              opacity: CurvedAnimation(
                parent: _loadingControllers[index],
                curve: Curves.easeInOut,
              ),
              child: Text('.', style: context.textTheme.primary),
            ),
          );
        }),
      ),
    );
  }
}

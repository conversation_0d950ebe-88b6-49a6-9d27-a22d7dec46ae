import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class CommonListTile extends StatelessWidget {
  final String title;
  final VoidCallback? onTap;
  final double? borderRadius;
  final String? value;
  final bool showBorder;
  final BorderRadiusGeometry? borderRadiusGeometry;
  final Color? titleColor;
  const CommonListTile(
      {super.key,
      required this.title,
      this.onTap,
      this.borderRadius,
      this.value,
      this.showBorder = true,
      this.borderRadiusGeometry,
      this.titleColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: borderRadiusGeometry ?? BorderRadius.circular(borderRadius ?? 8.gr),
      ),
      child: _TileItem(
        title: title,
        onTap: onTap,
        value: value,
        showBorder: showBorder,
        titleColor: titleColor,
      ),
    );
  }
}

class _TileItem extends StatelessWidget {
  const _TileItem({
    required this.title,
    this.onTap,
    this.titleColor,
    this.value,
    this.showBorder = true,
  });

  final String title;
  final VoidCallback? onTap;
  final String? value;
  final bool showBorder;
  final Color? titleColor;
  @override
  Widget build(BuildContext context) {
    return Bounceable(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 12.gw,
          vertical: 16.gh,
        ),
        decoration: BoxDecoration(
          border: showBorder
              ? Border(
                  bottom: BorderSide(
                    color: context.theme.cardColor,
                    width: 0.1,
                  ),
                )
              : null,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: context.textTheme.regular.copyWith(
                color: titleColor ?? context.colorTheme.textPrimary,
              ),
            ),
            Row(
              children: [
                if (value != null)
                  Text(
                    value!,
                    style: context.textTheme.regular.fs12.copyWith(
                      color: context.colorTheme.textRegular,
                    ),
                  ),
                8.horizontalSpace,
                if (onTap != null)
                  Icon(
                    Icons.chevron_right,
                    color: context.colorTheme.textRegular,
                    size: 24.gr,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;

import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/models/web_scoket_message.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_scoket_interface.dart';
import 'package:injectable/injectable.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'package:web_socket_channel/web_socket_channel.dart';

@Singleton(as: WebSocketService)

/// WebSocketChannelService provides a robust WebSocket connection management with
/// reconnection, heartbeat, event handling, and message queuing capabilities.
class WebSocketChannelService implements WebSocketService {
  WebSocketChannel? _channel;
  final StreamController<ConnectionState> _connectionStateController = StreamController<ConnectionState>.broadcast();
  final StreamController<WebSocketMessage> _messageController = StreamController<WebSocketMessage>.broadcast();
  final Map<String, List<Function>> _eventHandlers = {};
  final List<String> _loggedTypes = [SocketEvents.contract];

  ConnectionState _connectionState = ConnectionState.disconnected;
  StreamSubscription<dynamic>? _channelSubscription;
  final String _logTag = 'WebSocketChannelService';
  late String _url;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  final List<Completer<void>> _pendingMessages = [];

  // Configuration parameters
  final int reconnectInterval = 3000;
  final int heartbeatInterval = 10000;
  final int maxReconnectAttempts = 5;
  final bool autoReconnect = true;
  bool _enableLogging = true;
  int _reconnectAttempts = 0;

  // Function for reconnection failure notifications

  /// Constructor for WebSocketChannelService
  WebSocketChannelService() {
    _log('Initializing WebSocketChannelService');
  }

  /// Enables or disables logging
  void _log(String message, {Object? error}) {
    if (_enableLogging) {
      if (error != null) {
        dev.log(message, name: _logTag, error: error);
      } else {
        dev.log(message, name: _logTag);
      }
    }
  }

  @override
  void setLogging(bool enabled) {
    _enableLogging = enabled;
    _log('Logging set to $enabled');
  }

  @override
  ConnectionState get connectionState => _connectionState;

  @override
  Stream<ConnectionState> get onConnectionStateChanged => _connectionStateController.stream;

  @override
  Stream<WebSocketMessage> onMessageWithType(String type, {bool loginRequired = false}) =>
      _messageController.stream.where((event) {
        if (loginRequired) {
          return event.data != null && event.data['type'] == type && getIt<UserCubit>().state.isLogin;
        }
        return event.data != null && event.data['type'] == type;
      });

  @override
  Stream<WebSocketMessage> onMessageWithAction(String action, {bool loginRequired = false}) =>
      _messageController.stream.where((event) {
        if (loginRequired) {
          return event.data != null && event.data['action'] == action && getIt<UserCubit>().state.isLogin;
        }
        return event.data != null && event.data['action'] == action;
      });

  /// Connects to the WebSocket server with the specified URL and optional headers
  @override
  Future<void> connect(String url, {Map<String, dynamic>? headers}) async {
    if (_connectionState == ConnectionState.connected || _connectionState == ConnectionState.connecting) {
      _log('Already connected or connecting to: $url');
      return;
    }

    _url = url;
    _log('Connecting to WebSocket: $url');
    if (headers != null) {
      _log('Using headers: $headers');
    }

    _updateConnectionState(ConnectionState.connecting);

    try {
      final uri = Uri.parse(url);
      _channel = WebSocketChannel.connect(uri);

      _channelSubscription = _channel!.stream.listen(
        _onData,
        onError: _onError,
        onDone: _onDone,
      );

      _reconnectAttempts = 0;
      _startHeartbeat();
      _flushPendingMessages();
      _updateConnectionState(ConnectionState.connected);
      _log('Connection established successfully');
    } catch (e) {
      _log('Connection error: $e', error: e);
      _updateConnectionState(ConnectionState.error);
      if (autoReconnect) {
        _tryReconnect();
      }
      rethrow;
    }
  }

  /// Resets reconnection attempts counter
  void resetReconnectAttempts() {
    _reconnectAttempts = 0;
    _log('Reconnect attempts reset');
  }

  /// Sends data through the WebSocket connection, queuing if not connected
  @override
  Future<void> send(dynamic data) async {
    try {
      await _waitForConnection();

      if (_channel == null || _connectionState != ConnectionState.connected) {
        final error = StateError('Cannot send message when not connected');
        _log('Send failed: not connected', error: error);
        throw error;
      }

      final message = data is Map || data is List ? jsonEncode(data) : data;
      _log('Sending data: $message');
      _channel!.sink.add(message);
    } catch (e) {
      _log('Error sending data: $e', error: e);
      rethrow;
    }
  }

  /// Disconnects the WebSocket connection
  @override
  Future<void> disconnect() async {
    if (_connectionState == ConnectionState.disconnected) {
      _log('Already disconnected');
      return;
    }

    _log('Disconnecting WebSocket');
    _stopHeartbeat();
    _stopReconnect();
    await _channel?.sink.close(status.normalClosure);
    _cleanupChannel();
    _updateConnectionState(ConnectionState.disconnected);
  }

  /// Disposes of all resources used by the service
  @override
  Future<void> dispose() async {
    _log('Disposing WebSocketChannelService');
    await disconnect();
    await _connectionStateController.close();
    await _messageController.close();
  }

  /// Registers an event handler for a specific event type
  void on(String event, Function(dynamic) handler) {
    _eventHandlers[event] ??= [];
    _eventHandlers[event]!.add(handler);
    _log('Registered handler for event: $event');
  }

  /// Removes an event handler for a specific event type
  void off(String event, Function(dynamic) handler) {
    if (_eventHandlers[event] != null) {
      _eventHandlers[event]!.remove(handler);
      _log('Removed handler for event: $event');
    }
  }
  // Private methods

  /// Handles incoming WebSocket data
  void _onData(dynamic data) {
    _log('Received type: $data');
    if (data != 'pong') {
      try {
        final parsed = jsonDecode(data as String);
        _log('Received type: ${parsed['type']}');
        if (parsed is Map<String, dynamic> && parsed['type'] != null && _loggedTypes.contains(parsed['type'])) {
          _log('Received data: $data');
        }
      } catch (e) {
        _log('Error parsing message: $e');
      }
    }
    if (data != 'pong') {
      try {
        final parsed = jsonDecode(data as String);
        final message = WebSocketMessage(data: parsed);
        _messageController.add(message);

        // Trigger event handlers
        if (parsed is Map<String, dynamic>) {
          if (parsed['type'] != null) _triggerEvent(parsed['type'], parsed);
          if (parsed['action'] != null) _triggerEvent(parsed['action'], parsed);
        }
      } catch (e) {
        _log('Error parsing message: $e');
        _messageController.add(WebSocketMessage(data: data));
      }
    }
  }

  /// Handles WebSocket errors
  void _onError(dynamic error) {
    _log('WebSocket error: $error', error: error);
    _updateConnectionState(ConnectionState.error);
    _cleanupChannel();
    if (autoReconnect) {
      _tryReconnect();
    }
  }

  /// Handles WebSocket closure
  void _onDone() {
    _log('WebSocket connection closed');
    _updateConnectionState(ConnectionState.disconnected);
    _cleanupChannel();
    if (autoReconnect) {
      _tryReconnect();
    }
  }

  /// Updates and broadcasts the connection state
  void _updateConnectionState(ConnectionState state) {
    _connectionState = state;
    _connectionStateController.add(state);
    _log('Connection state updated: $state');
  }

  /// Cleans up channel resources
  void _cleanupChannel() {
    _channelSubscription?.cancel();
    _channelSubscription = null;
    _channel = null;
    _log('Channel cleaned up');
  }

  /// Initiates reconnection attempts
  void _tryReconnect() {
    if (_reconnectAttempts >= maxReconnectAttempts) {
      _log('Max reconnect attempts reached');
      _updateConnectionState(ConnectionState.reconnectFailed);

      // Notify about reconnection failure if callback provided

      // Resolve any pending messages with error
      _rejectPendingMessages(Exception('Max reconnect attempts reached'));
      return;
    }

    _stopReconnect();
    _reconnectAttempts++;
    _log('Reconnecting attempt $_reconnectAttempts');

    _reconnectTimer = Timer(Duration(milliseconds: reconnectInterval), () {
      connect(_url).catchError((error) {
        _log('Reconnect failed: $error');
      });
    });
  }

  /// Stops the reconnection timer
  void _stopReconnect() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Starts the heartbeat mechanism
  void _startHeartbeat() {
    _stopHeartbeat();
    _heartbeatTimer = Timer.periodic(Duration(milliseconds: heartbeatInterval), (_) {
      if (_connectionState == ConnectionState.connected) {
        try {
          _channel?.sink.add('ping');
          _log('Sent heartbeat ping');
        } catch (e) {
          _log('Error sending heartbeat: $e');
        }
      }
    });
  }

  /// Stops the heartbeat mechanism
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Waits for connection to be established before sending messages
  Future<void> _waitForConnection() {
    if (_connectionState == ConnectionState.connected) {
      return Future.value();
    }

    // If in reconnectFailed state, don't even try to wait
    if (_connectionState == ConnectionState.reconnectFailed) {
      return Future.error(StateError('Connection failed permanently'));
    }

    final completer = Completer<void>();

    // Add timeout to prevent indefinite waiting
    final timeoutDuration = Duration(milliseconds: reconnectInterval * maxReconnectAttempts);
    Timer(timeoutDuration, () {
      if (!completer.isCompleted) {
        completer.completeError(
            TimeoutException('Connection timed out after ${timeoutDuration.inMilliseconds}ms', timeoutDuration));
      }
    });

    _pendingMessages.add(completer);
    return completer.future;
  }

  /// Flushes pending messages when connection is established
  void _flushPendingMessages() {
    while (_pendingMessages.isNotEmpty) {
      final completer = _pendingMessages.removeAt(0);
      if (!completer.isCompleted) {
        completer.complete();
      }
    }
    _log('Flushed pending messages');
  }

  /// Rejects all pending messages with an error
  void _rejectPendingMessages(Exception error) {
    while (_pendingMessages.isNotEmpty) {
      final completer = _pendingMessages.removeAt(0);
      if (!completer.isCompleted) {
        completer.completeError(error);
      }
    }
    _log('Rejected all pending messages');
  }

  /// Triggers registered event handlers
  void _triggerEvent(String event, dynamic data) {
    final handlers = _eventHandlers[event];
    if (handlers != null && handlers.isNotEmpty) {
      for (var handler in handlers) {
        try {
          handler(data);
        } catch (e) {
          _log('Error in event handler for $event: $e', error: e);
          // Continue with other handlers
        }
      }
      _log('Triggered event: $event with data: $data');
    }
  }
}

/// Enum representing WebSocket connection states
enum ConnectionState {
  disconnected,
  connecting,
  connected,
  error,
  reconnectFailed,
}

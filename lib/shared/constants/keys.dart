class LocalStorageKeys {
  static const String token = 'token';
  static const String refreshToken = 'refreshToken';
  static const String user = 'user';
  static const String userV2 = 'userV2';
  static const String isRememberPassword = 'isRememberPassword';
  static const String username = 'username';
  static const String password = 'password';
}

class SharedPreferencesKeys {
  static const String isLoggedIn = 'isLoggedIn';
  static const String chatConfig = 'chatConfig';
}

const coinsText = {
  'BTCUSDT': 'Bitcoin',
  'ETHUSDT': 'Ethereum',
  'BNBUSDT': 'Binance Coin',
  'MATICUSDT': 'Polygon',
  'ATOMUSDT': 'Cosmos',
  'AVAXUSDT': 'Avalanche',
  'NEARUSDT': 'NEAR Protocol',
};

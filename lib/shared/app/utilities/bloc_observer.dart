import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/account/logic/add_bank/add_bank_cubit.dart';

import '../../../core/utils/log.dart';

class MyBlocObserver extends BlocObserver {
  // Add this list to store bloc types that should be ignored
  static const List<Type> _ignoredBlocs = [
    // Add your bloc types here, for example:
    // AuthBloc,
    // UserCubit,
    AddBankCubit
  ];

  // Helper method to check if bloc should be logged
  bool _shouldLog(BlocBase bloc) {
    return !_ignoredBlocs.contains(bloc.runtimeType);
  }

  @override
  void onCreate(BlocBase bloc) {
    super.onCreate(bloc);
    if (_shouldLog(bloc)) {
      LogI('${bloc.runtimeType} created');
    }
  }

  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    if (_shouldLog(bloc)) {
      LogI('${bloc.runtimeType} changed\n$change');
    }
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    if (_shouldLog(bloc)) {
      LogE('${bloc.runtimeType} error\n$error\n$stackTrace');
    }
    super.onError(bloc, error, stackTrace);
  }

  @override
  void onClose(BlocBase bloc) {
    super.onClose(bloc);
    if (_shouldLog(bloc)) {
      LogI('${bloc.runtimeType} closed');
    }
  }

  @override
  void onEvent(Bloc bloc, Object? event) {
    super.onEvent(bloc, event);
    if (_shouldLog(bloc)) {
      LogW('${bloc.runtimeType} event\n$event');
    }
  }

  @override
  void onTransition(Bloc bloc, Transition transition) {
    super.onTransition(bloc, transition);
    if (_shouldLog(bloc)) {
      LogF('${bloc.runtimeType} transition\n$transition');
    }
  }
}

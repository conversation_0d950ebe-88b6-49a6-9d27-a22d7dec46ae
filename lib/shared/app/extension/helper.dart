import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/features/sign_in/logic/sign_in/sign_in_cubit.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';

import '../../../core/api/network/models/bad_request.dart';
import '../../constants/assets.dart';
import '../../routes/routes.dart';

class Helper {
  static void afterInit(VoidCallback function) {
    WidgetsBinding.instance.addPostFrameCallback((_) => function());
  }

  String getLanguageName(Locale locale) => switch (locale) {
        Locale(languageCode: 'en') => 'English',
        Locale(languageCode: 'zh', countryCode: 'TW') => '中文繁体',
        Locale(languageCode: 'zh', countryCode: 'CN') => '中文简体',
        _ => locale.languageCode.toUpperCase(),
      };

  String getLanguageIcon(Locale locale) => switch (locale) {
        Locale(languageCode: 'en') => Assets.enLangIcon,
        Locale(languageCode: 'zh', countryCode: 'TW') => Assets.tradLangIcon,
        Locale(languageCode: 'zh', countryCode: 'CN') => Assets.simpleLangIcon,
        _ => Assets.enLangIcon,
      };

  // Map to track active dialogs by their keys
  static final Map<String, bool> _activeDialogs = {};

  /// Shows a dialog popup if one with the same dialogKey is not already showing.
  ///
  /// `context` The BuildContext
  /// `dialogWidget` The widget to display in the dialog
  /// `dialogKey` A unique key to identify this dialog (e.g., 'unauthorized_error', 'network_error')
  /// `barrierDismissible` Whether the dialog can be dismissed by tapping outside
  /// `routeName` Optional route name for the dialog
  /// @returns true if the dialog was shown, false if a dialog with the same key is already showing
  static bool showDialogPopUp(
    BuildContext context,
    Widget dialogWidget, {
    String? dialogKey,
    bool barrierDismissible = true,
    String? routeName,
  }) {
    // If dialogKey is provided, check if a dialog with this key is already showing
    if (dialogKey != null && _activeDialogs[dialogKey] == true) {
      // Dialog with this key is already showing, don't show another one
      return false;
    }

    // Mark this dialog as active if a key is provided
    if (dialogKey != null) {
      _activeDialogs[dialogKey] = true;
    }

    showGeneralDialog(
      barrierDismissible: barrierDismissible,
      context: context,
      barrierLabel: "",
      routeSettings: routeName == null ? null : RouteSettings(name: routeName),
      pageBuilder: (ctx, a1, a2) {
        return Container();
      },
      transitionBuilder: (ctx, a1, a2, child) {
        return PopScope(
          canPop: barrierDismissible,
          onPopInvokedWithResult: (didPop, result) async {
            if (barrierDismissible) {
              // Mark the dialog as inactive when it's closed
              if (dialogKey != null) {
                _activeDialogs.remove(dialogKey);
              }
            }
          },
          child: dialogWidget,
        );
      },
    ).then((_) {
      // Make sure to mark the dialog as inactive when it's closed
      if (dialogKey != null) {
        _activeDialogs.remove(dialogKey);
      }
    });

    return true;
  }

  dynamic errorMapping(Response? response) {
    final badRequest = <BadRequest>[]; // List to store BadRequest objects
    var errorString = ''; // String to accumulate error messages

    // Check if response and response.data are not null
    if (response?.data is Map) {
      // Check for specific error types
      if (response?.data.containsKey('msg')) {
        final message = response?.data['msg'];
        if (message is String) {
          badRequest.add(BadRequest(error: [message]));
        }
      } else if (response?.data.containsKey('error')) {
        final error = response?.data['error'];
        if (error is String) {
          badRequest.add(BadRequest(error: [error]));
        }
      }
    } else {
      // Handle case where response.data is null or not a Map
      badRequest.add(BadRequest(error: ['Invalid response format']));
    }

    // Construct error string from badRequest list
    for (var element in badRequest) {
      var subString = '';
      element.error?.forEach((sub) {
        subString = '$subString\n$sub';
      });
      if (errorString.isEmpty) {
        errorString = subString;
      } else {
        errorString = '$errorString\n\n$subString';
      }
    }

    return errorString;
  }

  String replaceCharacters(String text) => capitalizeFirstLetter(text.replaceAll(RegExp('[\\W_]+'), ' '));

  String capitalizeFirstLetter(String input) {
    if (input.isEmpty) {
      return input; // Return an empty string if the input is empty.
    }
    return input[0].toUpperCase() + input.substring(1);
  }

  Map<String, dynamic> removeNullValues(Map<String, dynamic> input) {
    return Map.fromEntries(input.entries.where((e) => e.value != null));
  }

  static Future<void> logoutUser(BuildContext context, {bool isNavToMain = false, bool needNav = true}) async {
    context.read<UserCubit>().setUserInfo(null);
    await context.read<SignInCubit>().logout();
    if (needNav) {
      if (isNavToMain) {
        Navigator.of(navigatorKey.currentContext!).pushNamedAndRemoveUntil(routeMain, (route) => false);
      } else {
        Navigator.of(navigatorKey.currentContext!).pushNamedAndRemoveUntil(routeLogin, (route) => route.isFirst);
      }
    }
  }

  static void showFlutterToast(
    String message, {
    ToastGravity gravity = ToastGravity.CENTER,
  }) {
    Fluttertoast.showToast(
      msg: message,
      toastLength: Toast.LENGTH_SHORT,
      gravity: gravity,
      timeInSecForIosWeb: 1,
      backgroundColor: const Color.fromRGBO(0, 0, 0, 0.5),
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }
}

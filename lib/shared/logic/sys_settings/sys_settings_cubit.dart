import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/services/common_services.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';
import 'package:gp_stock_app/shared/models/sys_settings_model/sys_config_model.dart';
import 'package:gp_stock_app/shared/models/sys_settings_model/sys_settings_model.dart';

part 'sys_settings_cubit.freezed.dart';
part 'sys_settings_state.dart';

class SysSettingsCubit extends Cubit<SysSettingsState> {
  SysSettingsCubit() : super(const SysSettingsState.initial());

  final CommonServices _commonServices = CommonServices();

  Future<void> fetchSysSettings() async {
    try {
      emit(const SysSettingsState.loading());

      final response = await _commonServices.getSysSettings();
      final sysConfigModel = await CommonServices.getSysConfig();
      final defaultConfig = SysConfigModel()..tradingMode = AppConfig.instance.tradingMode.customIndex;

      if (response.data != null) {
        emit(SysSettingsState.loaded(
          sysSettings: response.data!,
          sysConfigModel: sysConfigModel ?? defaultConfig,
        ));
      } else {
        emit(SysSettingsState.error(response.error ?? "Failed to fetch system settings"));
      }
    } catch (e) {
      emit(SysSettingsState.error("Failed to fetch system settings: ${e.toString()}"));
    }
  }
}

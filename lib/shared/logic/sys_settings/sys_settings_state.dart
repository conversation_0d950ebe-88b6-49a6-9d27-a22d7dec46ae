part of 'sys_settings_cubit.dart';

@freezed
class SysSettingsState with _$SysSettingsState {
  const factory SysSettingsState.initial() = _Initial;

  const factory SysSettingsState.loaded({
    required SysSettingsModel sysSettings,
    required SysConfigModel sysConfigModel,
  }) = _Loaded;

  const factory SysSettingsState.error(String message) = _Error;

  const factory SysSettingsState.loading() = _Loading;
}

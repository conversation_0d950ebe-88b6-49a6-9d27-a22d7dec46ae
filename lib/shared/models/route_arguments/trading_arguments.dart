import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';

class TradingArguments {
  final Instrument instrumentInfo;
  final int selectedIndex;
  final bool shouldNavigateToIndex;
  final bool isIndexTrading;
  final ContractSummaryData? contract;
  final String? tradeType;
  final bool isFromContractDetails;
  TradingArguments({
    required this.instrumentInfo,
    required this.selectedIndex,
    this.shouldNavigateToIndex = false,
    this.isIndexTrading = false,
    this.contract,
    this.tradeType,
    this.isFromContractDetails = false,
  });
}

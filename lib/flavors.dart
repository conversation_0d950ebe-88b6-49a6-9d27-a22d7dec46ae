enum Flavor {
  pre,
  gp,
  rsyp,
  yhxt,
  tempa,
  bszb,
}

class F {
  static late final Flavor appFlavor;

  static String get name => appFlavor.name;

  static String get title {
    switch (appFlavor) {
      case Flavor.pre:
        return 'GP Pre';
      case Flavor.gp:
        return 'GP Stock';
      case Flavor.rsyp:
        return '荣顺优配';
      case Flavor.yhxt:
        return '沅和信投';
      case Flavor.tempa:
        return 'tempa';
      case Flavor.bszb:
        return '宝石资本';
    }
  }

}

import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';

FTradeStateModel $FTradeStateModelFromJson(Map<String, dynamic> json) {
  final FTradeStateModel fTradeStateModel = FTradeStateModel();
  final String? statusStr = jsonConvert.convert<String>(json['statusStr']);
  if (statusStr != null) {
    fTradeStateModel.statusStr = statusStr;
  }
  final int? closeTime = jsonConvert.convert<int>(json['closeTime']);
  if (closeTime != null) {
    fTradeStateModel.closeTime = closeTime;
  }
  final int? openTime = jsonConvert.convert<int>(json['openTime']);
  if (openTime != null) {
    fTradeStateModel.openTime = openTime;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    fTradeStateModel.status = status;
  }
  return fTradeStateModel;
}

Map<String, dynamic> $FTradeStateModelToJson(FTradeStateModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['statusStr'] = entity.statusStr;
  data['closeTime'] = entity.closeTime;
  data['openTime'] = entity.openTime;
  data['status'] = entity.status;
  return data;
}

extension FTradeStateModelExtension on FTradeStateModel {
  FTradeStateModel copyWith({
    String? statusStr,
    int? closeTime,
    int? openTime,
    int? status,
  }) {
    return FTradeStateModel()
      ..statusStr = statusStr ?? this.statusStr
      ..closeTime = closeTime ?? this.closeTime
      ..openTime = openTime ?? this.openTime
      ..status = status ?? this.status;
  }
}

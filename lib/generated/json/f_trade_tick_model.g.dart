import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_tick_model.dart';

FTradeTickModel $FTradeTickModelFromJson(Map<String, dynamic> json) {
  final FTradeTickModel fTradeTickModel = FTradeTickModel();
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    fTradeTickModel.current = current;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    fTradeTickModel.total = total;
  }
  final List<FTradeTickRecords>? records = (json['records'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<FTradeTickRecords>(e) as FTradeTickRecords).toList();
  if (records != null) {
    fTradeTickModel.records = records;
  }
  final bool? hasNext = jsonConvert.convert<bool>(json['hasNext']);
  if (hasNext != null) {
    fTradeTickModel.hasNext = hasNext;
  }
  return fTradeTickModel;
}

Map<String, dynamic> $FTradeTickModelToJson(FTradeTickModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['total'] = entity.total;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['hasNext'] = entity.hasNext;
  return data;
}

extension FTradeTickModelExtension on FTradeTickModel {
  FTradeTickModel copyWith({
    int? current,
    int? total,
    List<FTradeTickRecords>? records,
    bool? hasNext,
  }) {
    return FTradeTickModel()
      ..current = current ?? this.current
      ..total = total ?? this.total
      ..records = records ?? this.records
      ..hasNext = hasNext ?? this.hasNext;
  }
}

FTradeTickRecords $FTradeTickRecordsFromJson(Map<String, dynamic> json) {
  final FTradeTickRecords fTradeTickRecords = FTradeTickRecords();
  final double? tradeVolume = jsonConvert.convert<double>(json['tradeVolume']);
  if (tradeVolume != null) {
    fTradeTickRecords.tradeVolume = tradeVolume;
  }
  final double? tradeTime = jsonConvert.convert<double>(json['tradeTime']);
  if (tradeTime != null) {
    fTradeTickRecords.tradeTime = tradeTime;
  }
  final String? time = jsonConvert.convert<String>(json['time']);
  if (time != null) {
    fTradeTickRecords.time = time;
  }
  final double? tradePrice = jsonConvert.convert<double>(json['tradePrice']);
  if (tradePrice != null) {
    fTradeTickRecords.tradePrice = tradePrice;
  }
  final String? direction = jsonConvert.convert<String>(json['direction']);
  if (direction != null) {
    fTradeTickRecords.direction = direction;
  }
  return fTradeTickRecords;
}

Map<String, dynamic> $FTradeTickRecordsToJson(FTradeTickRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['tradeVolume'] = entity.tradeVolume;
  data['tradeTime'] = entity.tradeTime;
  data['time'] = entity.time;
  data['tradePrice'] = entity.tradePrice;
  data['direction'] = entity.direction;
  return data;
}

extension FTradeTickRecordsExtension on FTradeTickRecords {
  FTradeTickRecords copyWith({
    double? tradeVolume,
    double? tradeTime,
    String? time,
    double? tradePrice,
    String? direction,
  }) {
    return FTradeTickRecords()
      ..tradeVolume = tradeVolume ?? this.tradeVolume
      ..tradeTime = tradeTime ?? this.tradeTime
      ..time = time ?? this.time
      ..tradePrice = tradePrice ?? this.tradePrice
      ..direction = direction ?? this.direction;
  }
}
import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';

FTradeInfoModel $FTradeInfoModelFromJson(Map<String, dynamic> json) {
  final FTradeInfoModel fTradeInfoModel = FTradeInfoModel();
  final String? symbol = jsonConvert.convert<String>(json['symbol']);
  if (symbol != null) {
    fTradeInfoModel.symbol = symbol;
  }
  final int? precision = jsonConvert.convert<int>(json['precision']);
  if (precision != null) {
    fTradeInfoModel.precision = precision;
  }
  final double? settle = jsonConvert.convert<double>(json['settle']);
  if (settle != null) {
    fTradeInfoModel.settle = settle;
  }
  final int? securityStatus = jsonConvert.convert<int>(json['securityStatus']);
  if (securityStatus != null) {
    fTradeInfoModel.securityStatus = securityStatus;
  }
  final double? gain = jsonConvert.convert<double>(json['gain']);
  if (gain != null) {
    fTradeInfoModel.gain = gain;
  }
  final double? high = jsonConvert.convert<double>(json['high']);
  if (high != null) {
    fTradeInfoModel.high = high;
  }
  final double? low = jsonConvert.convert<double>(json['low']);
  if (low != null) {
    fTradeInfoModel.low = low;
  }
  final int? contractSize = jsonConvert.convert<int>(json['contractSize']);
  if (contractSize != null) {
    fTradeInfoModel.contractSize = contractSize;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    fTradeInfoModel.currency = currency;
  }
  final bool? isContinuous = jsonConvert.convert<bool>(json['isContinuous']);
  if (isContinuous != null) {
    fTradeInfoModel.isContinuous = isContinuous;
  }
  final double? close = jsonConvert.convert<double>(json['close']);
  if (close != null) {
    fTradeInfoModel.close = close;
  }
  final double? latestPrice = jsonConvert.convert<double>(json['latestPrice']);
  if (latestPrice != null) {
    fTradeInfoModel.latestPrice = latestPrice;
  }
  final int? amount = jsonConvert.convert<int>(json['amount']);
  if (amount != null) {
    fTradeInfoModel.amount = amount;
  }
  final double? chg = jsonConvert.convert<double>(json['chg']);
  if (chg != null) {
    fTradeInfoModel.chg = chg;
  }
  final bool? isMain = jsonConvert.convert<bool>(json['isMain']);
  if (isMain != null) {
    fTradeInfoModel.isMain = isMain;
  }
  final int? lotSize = jsonConvert.convert<int>(json['lotSize']);
  if (lotSize != null) {
    fTradeInfoModel.lotSize = lotSize;
  }
  final int? priceUpLimited = jsonConvert.convert<int>(json['priceUpLimited']);
  if (priceUpLimited != null) {
    fTradeInfoModel.priceUpLimited = priceUpLimited;
  }
  final double? priceDownLimited = jsonConvert.convert<double>(json['priceDownLimited']);
  if (priceDownLimited != null) {
    fTradeInfoModel.priceDownLimited = priceDownLimited;
  }
  final String? market = jsonConvert.convert<String>(json['market']);
  if (market != null) {
    fTradeInfoModel.market = market;
  }
  final int? volume = jsonConvert.convert<int>(json['volume']);
  if (volume != null) {
    fTradeInfoModel.volume = volume;
  }
  final int? expireTime = jsonConvert.convert<int>(json['expireTime']);
  if (expireTime != null) {
    fTradeInfoModel.expireTime = expireTime;
  }
  final String? productCode = jsonConvert.convert<String>(json['productCode']);
  if (productCode != null) {
    fTradeInfoModel.productCode = productCode;
  }
  final String? securityType = jsonConvert.convert<String>(json['securityType']);
  if (securityType != null) {
    fTradeInfoModel.securityType = securityType;
  }
  final int? lastTradeTime = jsonConvert.convert<int>(json['lastTradeTime']);
  if (lastTradeTime != null) {
    fTradeInfoModel.lastTradeTime = lastTradeTime;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    fTradeInfoModel.name = name;
  }
  final int? latestTime = jsonConvert.convert<int>(json['latestTime']);
  if (latestTime != null) {
    fTradeInfoModel.latestTime = latestTime;
  }
  final int? position = jsonConvert.convert<int>(json['position']);
  if (position != null) {
    fTradeInfoModel.position = position;
  }
  final String? contractCode = jsonConvert.convert<String>(json['contractCode']);
  if (contractCode != null) {
    fTradeInfoModel.contractCode = contractCode;
  }
  final int? prevPosition = jsonConvert.convert<int>(json['prevPosition']);
  if (prevPosition != null) {
    fTradeInfoModel.prevPosition = prevPosition;
  }
  final double? open = jsonConvert.convert<double>(json['open']);
  if (open != null) {
    fTradeInfoModel.open = open;
  }
  return fTradeInfoModel;
}

Map<String, dynamic> $FTradeInfoModelToJson(FTradeInfoModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['symbol'] = entity.symbol;
  data['precision'] = entity.precision;
  data['settle'] = entity.settle;
  data['securityStatus'] = entity.securityStatus;
  data['gain'] = entity.gain;
  data['high'] = entity.high;
  data['low'] = entity.low;
  data['contractSize'] = entity.contractSize;
  data['currency'] = entity.currency;
  data['isContinuous'] = entity.isContinuous;
  data['close'] = entity.close;
  data['latestPrice'] = entity.latestPrice;
  data['amount'] = entity.amount;
  data['chg'] = entity.chg;
  data['isMain'] = entity.isMain;
  data['lotSize'] = entity.lotSize;
  data['priceUpLimited'] = entity.priceUpLimited;
  data['priceDownLimited'] = entity.priceDownLimited;
  data['market'] = entity.market;
  data['volume'] = entity.volume;
  data['expireTime'] = entity.expireTime;
  data['productCode'] = entity.productCode;
  data['securityType'] = entity.securityType;
  data['lastTradeTime'] = entity.lastTradeTime;
  data['name'] = entity.name;
  data['latestTime'] = entity.latestTime;
  data['position'] = entity.position;
  data['contractCode'] = entity.contractCode;
  data['prevPosition'] = entity.prevPosition;
  data['open'] = entity.open;
  return data;
}

extension FTradeInfoModelExtension on FTradeInfoModel {
  FTradeInfoModel copyWith({
    String? symbol,
    int? precision,
    double? settle,
    int? securityStatus,
    double? gain,
    double? high,
    double? low,
    int? contractSize,
    String? currency,
    bool? isContinuous,
    double? close,
    double? latestPrice,
    int? amount,
    double? chg,
    bool? isMain,
    int? lotSize,
    int? priceUpLimited,
    double? priceDownLimited,
    String? market,
    int? volume,
    int? expireTime,
    String? productCode,
    String? securityType,
    int? lastTradeTime,
    String? name,
    int? latestTime,
    int? position,
    String? contractCode,
    int? prevPosition,
    double? open,
  }) {
    return FTradeInfoModel()
      ..symbol = symbol ?? this.symbol
      ..precision = precision ?? this.precision
      ..settle = settle ?? this.settle
      ..securityStatus = securityStatus ?? this.securityStatus
      ..gain = gain ?? this.gain
      ..high = high ?? this.high
      ..low = low ?? this.low
      ..contractSize = contractSize ?? this.contractSize
      ..currency = currency ?? this.currency
      ..isContinuous = isContinuous ?? this.isContinuous
      ..close = close ?? this.close
      ..latestPrice = latestPrice ?? this.latestPrice
      ..amount = amount ?? this.amount
      ..chg = chg ?? this.chg
      ..isMain = isMain ?? this.isMain
      ..lotSize = lotSize ?? this.lotSize
      ..priceUpLimited = priceUpLimited ?? this.priceUpLimited
      ..priceDownLimited = priceDownLimited ?? this.priceDownLimited
      ..market = market ?? this.market
      ..volume = volume ?? this.volume
      ..expireTime = expireTime ?? this.expireTime
      ..productCode = productCode ?? this.productCode
      ..securityType = securityType ?? this.securityType
      ..lastTradeTime = lastTradeTime ?? this.lastTradeTime
      ..name = name ?? this.name
      ..latestTime = latestTime ?? this.latestTime
      ..position = position ?? this.position
      ..contractCode = contractCode ?? this.contractCode
      ..prevPosition = prevPosition ?? this.prevPosition
      ..open = open ?? this.open;
  }
}
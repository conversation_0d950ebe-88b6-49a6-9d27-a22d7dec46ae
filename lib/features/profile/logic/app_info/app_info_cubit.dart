import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/repository/profile_repository.dart';
import 'app_info_state.dart';

@singleton
class AppInfoCubit extends Cubit<AppInfoState> {
  final ProfileRepository _repository;

  AppInfoCubit(this._repository) : super(const AppInfoState());

  Future<void> getAppInfoList() async {
    emit(state.copyWith(status: DataStatus.loading));

    final result = await _repository.getAppInfoList();

    if (result.data != null) {
      emit(state.copyWith(
        status: DataStatus.success,
        appInfoList: result.data!,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        status: DataStatus.failed,
        error: result.error,
      ));
    }
  }
}

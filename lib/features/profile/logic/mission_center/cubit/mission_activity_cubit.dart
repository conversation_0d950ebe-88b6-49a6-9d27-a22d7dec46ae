import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/features/profile/domain/services/mission_activity_service.dart';
import 'package:injectable/injectable.dart';

import '../../../../../shared/app/utilities/easy_loading.dart';
import '../../../../../shared/constants/enums.dart';
import '../../../domain/models/mission_activity/sign_in_model.dart';

part 'mission_activity_state.dart';

@singleton
class MissionActivityCubit extends Cubit<MissionActivityState> {
  MissionActivityCubit() : super(const MissionActivityState());
  final _repository = MissionActivityService();

  Future<void> signIn() async {
    emit(state.copyWith(signInStatus: DataStatus.loading, updatingField: MissionActivityField.signIn));

    GPEasyLoading.showLoading(message: 'signingIn'.tr());

    final result = await _repository.signIn();
    if (result.isSuccess) {
      await getSignInLog();
      GPEasyLoading.showSuccess(message: 'signInSuccess'.tr());
      emit(state.copyWith(
        signInStatus: DataStatus.success,
        error: null,
      ));
    } else {
      GPEasyLoading.showToast(
        result.error ?? 'signInFailed'.tr(),
      );
      emit(state.copyWith(signInStatus: DataStatus.failed, error: result.error));
    }
  }

  Future<void> getSignInLog() async {
    if (state.signInLogStatus == DataStatus.loading) {
      LogI('getSignInLog skipping duplicate call');
      return;
    }

    emit(state.copyWith(signInLogStatus: DataStatus.loading));

    try {
      final result = await _repository.getSignInLog();

      if (result.isSuccess) {
        final signInDays = result.data ?? [];
        final today = DateTime.now();
        final todayStr = DateFormat('yyyy-MM-dd').format(today);

        final hasSign = signInDays.any((day) => day.date == todayStr && (day.hasSign ?? false));

        int consecutiveDays = 0;
        for (var day in signInDays.reversed) {
          if (day.hasSign ?? false) {
            consecutiveDays++;
          } else {
            break;
          }
        }

        emit(state.copyWith(
          signInLogStatus: DataStatus.success,
          signInDays: signInDays,
          hasSign: hasSign,
          consecutiveDays: consecutiveDays,
          currentMonth: today,
          error: null,
        ));
      } else {
        emit(state.copyWith(
          signInLogStatus: DataStatus.failed,
          error: result.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        signInLogStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }
}

part of 'profile_cubit.dart';

enum ProfileUpdateField { avatar, nickname, gender, email, bio, password, mobile }

class ProfileState extends Equatable {
  final DataStatus updateStatus;
  final String? error;
  final ProfileUpdateField? updatingField;
  final UserModel? userData;
  final int? selectedAvatarIndex;

  const ProfileState({
    this.updateStatus = DataStatus.idle,
    this.error,
    this.updatingField,
    this.userData,
    this.selectedAvatarIndex,
  });

  @override
  List<Object?> get props => [
        updateStatus,
        error,
        updatingField,
        userData,
        selectedAvatarIndex,
      ];

  ProfileState copyWith({
    DataStatus? updateStatus,
    String? error,
    ProfileUpdateField? updatingField,
    UserModel? userData,
    int? selectedAvatarIndex,
  }) {
    return ProfileState(
      updateStatus: updateStatus ?? this.updateStatus,
      error: error ?? this.error,
      updatingField: updatingField ?? this.updatingField,
      userData: userData ?? this.userData,
      selectedAvatarIndex: selectedAvatarIndex ?? this.selectedAvatarIndex,
    );
  }
}

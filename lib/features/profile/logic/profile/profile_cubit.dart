import 'dart:async';
import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/auth.dart';
import 'package:gp_stock_app/core/models/entities/user.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_scoket_interface.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/models/result.dart';
import '../../../../core/utils/secure_storage_helper.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/constants/keys.dart';
import '../../domain/repository/profile_repository.dart';

part 'profile_state.dart';

@singleton
class ProfileCubit extends AuthAwareCubit<ProfileState> {
  final ProfileRepository _repository;
  final WebSocketService _webSocketService = getIt<WebSocketService>();

  ProfileCubit(this._repository) : super(const ProfileState()) {
    _webSocketService.onMessageWithType(SocketEvents.updateProfile, loginRequired: true).listen((message) {
      getUserInfo();
    });
  }

  @override
  void onLoggedIn() {
    _loadInitialUserData();
    getUserInfo();
  }

  @override
  void onLoggedOut() => emit(const ProfileState());

  Future<void> _loadInitialUserData() async {
    final userData = getIt<UserCubit>().state.userInfo;
    if (userData != null) {
      final currentAvatarIndex = int.tryParse(userData.avatar);
      emit(state.copyWith(
        userData: userData,
        selectedAvatarIndex: currentAvatarIndex,
      ));
    }
  }

  void selectAvatar(int index) {
    emit(state.copyWith(selectedAvatarIndex: index));
  }

  Future<void> confirmAvatarSelection() async {
    if (state.selectedAvatarIndex == null) return;

    final avatarIndex = '${state.selectedAvatarIndex!}';
    await updateAvatar(avatarIndex);
  }

  Future<void> updateAvatar(String avatar) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.avatar,
    ));

    final result = await _repository.updateUserInfo(avatar: avatar);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserData = state.userData;
      if (currentUserData != null) {
        final updatedUserData = currentUserData.copyWith(avatar: avatar);
        await SecureStorageHelper().writeSecureData(
          LocalStorageKeys.userV2,
          jsonEncode(updatedUserData.toJson()),
        );
        emit(state.copyWith(userData: updatedUserData));
      }
    }
  }

  Future<void> updateNickname(String nickname) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.nickname,
    ));

    final result = await _repository.updateUserInfo(nickname: nickname);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserData = state.userData;
      if (currentUserData != null) {
        final updatedUserData = currentUserData.copyWith(nickname: nickname);
        await SecureStorageHelper().writeSecureData(
          LocalStorageKeys.userV2,
          jsonEncode(updatedUserData.toJson()),
        );
        emit(state.copyWith(userData: updatedUserData));
      }
    }
  }

  Future<void> updateGender(int sex) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.gender,
    ));

    final result = await _repository.updateUserInfo(sex: sex);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserData = state.userData;
      if (currentUserData != null) {
        final updatedUserData = currentUserData.copyWith(sex: sex);
        await SecureStorageHelper().writeSecureData(
          LocalStorageKeys.userV2,
          jsonEncode(updatedUserData.toJson()),
        );
        emit(state.copyWith(userData: updatedUserData));
      }
    }
  }

  Future<void> updateEmail(String email) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.email,
    ));

    final result = await _repository.updateUserInfo(email: email);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserData = state.userData;
      if (currentUserData != null) {
        final updatedUserData = currentUserData.copyWith(email: email);
        await SecureStorageHelper().writeSecureData(
          LocalStorageKeys.userV2,
          jsonEncode(updatedUserData.toJson()),
        );
        emit(state.copyWith(userData: updatedUserData));
      }
    }
  }

  Future<void> updatePhoneNumber(String phoneNo) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.bio,
    ));

    final result = await _repository.updateUserInfo(phoneNo: phoneNo);
    _handleUpdateResult(result);
    if (state.updateStatus == DataStatus.success) {
      final currentUserData = state.userData;
      if (currentUserData != null) {
        final updatedUserData = currentUserData.copyWith(profiles: phoneNo); //TODO: Modify to phone number
        await SecureStorageHelper().writeSecureData(
          LocalStorageKeys.userV2,
          jsonEncode(updatedUserData.toJson()),
        );
        emit(state.copyWith(userData: updatedUserData));
      }
    }
  }

  Future<bool> changePassword({
    required String password,
    String? originalPassword,
    String? smsCode,
    PasswordChangeType type = PasswordChangeType.accountVerification,
    PasswordType passwordType = PasswordType.account,
  }) async {
    final userData = state.userData;
    if (userData == null) {
      emit(state.copyWith(
        updateStatus: DataStatus.failed,
        error: 'userDataNotFound'.tr(),
      ));
      return false;
    }

    if (type == PasswordChangeType.smsVerification && (userData.mobile.isEmpty)) {
      emit(state.copyWith(
        updateStatus: DataStatus.failed,
        error: 'Mobile number not found',
      ));
      return false;
    }

    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.password,
    ));

    final base64NewPassword = password.toBase64();
    ResponseResult<bool> result;

    if (type == PasswordChangeType.accountVerification) {
      if (originalPassword == null) {
        emit(state.copyWith(
          updateStatus: DataStatus.failed,
          error: 'Original password required',
        ));
        return false;
      }

      final base64OriginalPassword = originalPassword.toBase64();
      result = await _repository.changePassword(
        password: base64OriginalPassword,
        newPassword: base64NewPassword,
        verifyType: "account",
        passwordType: passwordType,
      );
    } else {
      if (smsCode == null) {
        emit(state.copyWith(
          updateStatus: DataStatus.failed,
          error: 'SMS verification code required',
        ));
        return false;
      }

      result = await _repository.changePassword(
        mobile: userData.mobile,
        newPassword: base64NewPassword,
        smsCode: smsCode,
        verifyType: "mobile",
        passwordType: passwordType,
      );
    }

    if (result.data != null) {
      emit(state.copyWith(
        updateStatus: DataStatus.success,
        error: null,
      ));
      return true;
    }

    emit(state.copyWith(
      updateStatus: DataStatus.failed,
      error: result.error,
    ));
    return false;
  }

  void _handleUpdateResult(ResponseResult<bool> result) {
    if (result.data != null) {
      emit(state.copyWith(
        updateStatus: DataStatus.success,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        updateStatus: DataStatus.failed,
        error: result.error,
      ));
    }
  }

  Future<bool> updateMobile({
    required String countryCode,
    required String currentMobile,
    required String newMobile,
    required String smsCode,
  }) async {
    emit(state.copyWith(
      updateStatus: DataStatus.loading,
      updatingField: ProfileUpdateField.mobile,
    ));

    final result = await _repository.updateMobile(
      countryCode: countryCode,
      currentMobile: currentMobile,
      newMobile: newMobile,
      smsCode: smsCode,
    );

    if (result.isSuccess) {
      emit(state.copyWith(
        updateStatus: DataStatus.success,
        error: null,
      ));

      // Update the user data with the new mobile number
      final currentUserData = state.userData;
      if (currentUserData != null) {
        final updatedUserData = currentUserData.copyWith(mobile: newMobile);
        await SecureStorageHelper().writeSecureData(
          LocalStorageKeys.userV2,
          jsonEncode(updatedUserData.toJson()),
        );
        emit(state.copyWith(userData: updatedUserData));
      }
      return true;
    } else {
      emit(state.copyWith(
        updateStatus: DataStatus.failed,
        error: result.error,
      ));
      return false;
    }
  }

  Future<void> getUserInfo() async {
    final  res = await AuthApi.getUserInfo();
    if (res != null) {
      emit(state.copyWith(
        userData: res, // Update the userData state
      ));
      await SecureStorageHelper().writeSecureData(
        LocalStorageKeys.userV2,
        jsonEncode(res.toJson()),
      );
    }
  }
}

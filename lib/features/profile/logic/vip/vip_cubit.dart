import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/utils/log.dart';
import '../../../../shared/constants/enums.dart';
import '../../domain/repository/mission_activity_repository.dart';
import 'vip_state.dart';

@injectable
class VipCubit extends Cubit<VipState> {
  final MissionActivityRepository _repository;

  VipCubit(this._repository) : super(const VipState());

  Future<void> getUserLevelConfig() async {
    if (state.vipConfigStatus == DataStatus.loading) {
      LogI('getUserLevelConfig skipping duplicate call');
      return;
    }

    emit(state.copyWith(vipConfigStatus: DataStatus.loading));
    final result = await _repository.getUserLevelConfig();

    if (result.data != null) {
      emit(state.copyWith(
        vipConfigStatus: DataStatus.success,
        vipConfig: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        vipConfigStatus: DataStatus.failed,
        error: result.error,
      ));
    }
  }

  Future<void> getNextUserLevel() async {
    if (state.nextLevelStatus == DataStatus.loading) {
      LogI('getNextUserLevel skipping duplicate call');
      return;
    }

    emit(state.copyWith(nextLevelStatus: DataStatus.loading));
    final result = await _repository.getNextUserLevel();

    if (result.data != null) {
      emit(state.copyWith(
        nextLevelStatus: DataStatus.success,
        nextUserLevel: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(
        nextLevelStatus: DataStatus.failed,
        error: result.error,
      ));
    }
  }
}

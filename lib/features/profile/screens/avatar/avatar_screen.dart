import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../shared/app/utilities/easy_loading.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/widgets/buttons/custom_material_button.dart';
import '../../logic/profile/profile_cubit.dart';
import '../../widgets/settings/settings_appbar.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class AvatarScreen extends StatelessWidget {
  const AvatarScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: SettingsAppBar(title: 'avatar'.tr()),
      body: Column(
        children: [
          Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.gw),
              child: _AvatarGrid(),
            ),
          ),
          _ModifyButton(),
        ],
      ),
    );
  }
}

class _AvatarGrid extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      buildWhen: (previous, current) =>
          previous.userData?.avatar != current.userData?.avatar ||
          previous.selectedAvatarIndex != current.selectedAvatarIndex,
      builder: (context, state) {
        return GridView.builder(
          padding: EdgeInsets.symmetric(vertical: 16.gh),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5,
            mainAxisSpacing: 16.gh,
            crossAxisSpacing: 16.gw,
          ),
          itemCount: 30,
          itemBuilder: (context, index) {
            final itemIndex = index + 1;
            return _AvatarItem(
              index: itemIndex,
              isSelected: state.selectedAvatarIndex == itemIndex,
            );
          },
        );
      },
    );
  }
}

class _AvatarItem extends StatelessWidget {
  final int index;
  final bool isSelected;

  const _AvatarItem({
    required this.index,
    required this.isSelected,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, ProfileState>(
      buildWhen: (previous, current) => previous.selectedAvatarIndex != current.selectedAvatarIndex,
      builder: (context, state) {
        final isSelected = state.selectedAvatarIndex == index;

        return GestureDetector(
          onTap: () {
            context.read<ProfileCubit>().selectAvatar(index);
          },
          child: Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _getBackgroundColor(index),
                ),
                child: CircleAvatar(
                  radius: 28.gr,
                  backgroundColor: Colors.transparent,
                  backgroundImage: AssetImage(
                    'assets/images/avatars/$index.png',
                  ),
                ),
              ),
              if (isSelected)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: EdgeInsets.all(4.gr),
                    decoration: BoxDecoration(
                      color: context.theme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check,
                      size: 12.gw,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Color _getBackgroundColor(int index) {
    final colors = [
      const Color(0xFFE3F2FD),
      const Color(0xFFF3E5F5),
      const Color(0xFFE8F5E9),
      const Color(0xFFFFF3E0),
      const Color(0xFFEDE7F6),
    ];
    return colors[index % colors.length];
  }
}

class _ModifyButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProfileCubit, ProfileState>(
      listenWhen: (previous, current) =>
          previous.updateStatus != current.updateStatus && current.updatingField == ProfileUpdateField.avatar,
      listener: (context, state) {
        if (state.updateStatus == DataStatus.success) {
          GPEasyLoading.showSuccess(message: 'profileAvatarUpdateSuccess'.tr());
          Navigator.pop(context);
        }
      },
      buildWhen: (previous, current) =>
          previous.selectedAvatarIndex != current.selectedAvatarIndex ||
          (previous.updateStatus != current.updateStatus && current.updatingField == ProfileUpdateField.avatar),
      builder: (context, state) {
        final isLoading = state.updateStatus == DataStatus.loading && state.updatingField == ProfileUpdateField.avatar;
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gh),
          decoration: BoxDecoration(
            color: context.theme.scaffoldBackgroundColor,
            border: Border(
              top: BorderSide(
                color: context.theme.dividerColor,
                width: 1,
              ),
            ),
          ),
          child: CustomMaterialButton(
            buttonText: 'confirmChanges'.tr(),
            isLoading: isLoading,
            onPressed:
                state.selectedAvatarIndex == null ? null : () => context.read<ProfileCubit>().confirmAvatarSelection(),
          ),
        );
      },
    );
  }
}

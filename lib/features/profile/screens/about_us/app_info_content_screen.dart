import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gp_stock_app/features/profile/domain/models/app_info/app_info_model.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class AppInfoContentScreen extends StatelessWidget {
  final AppInfoModel appInfo;

  const AppInfoContentScreen({super.key, required this.appInfo});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(appInfo.title, style: context.textTheme.primary.fs16.w500),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Html(
              data: appInfo.content,
              style: {
                'span': Style(
                  color: context.colorTheme.textRegular,
                ),
                'p': Style(
                  color: context.colorTheme.textRegular,
                ),
                'body': Style(
                  color: context.colorTheme.textRegular,
                ),
              },
            ),
          ],
        ),
      ),
    );
  }
}

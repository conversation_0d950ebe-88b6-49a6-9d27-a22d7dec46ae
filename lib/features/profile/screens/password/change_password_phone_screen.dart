import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/validators.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/custom_pin_keyboard.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:pinput/pinput.dart';

import '../../../../shared/app/utilities/easy_loading.dart';


import '../../../../shared/widgets/otp_field.dart';
import '../../../account/logic/otp/otp_cubit.dart';
import '../../../account/logic/otp/otp_state.dart';
import '../../../main/widgets/draggable_float_widget.dart';
import '../../logic/profile/profile_cubit.dart';
import '../../widgets/settings/settings_appbar.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ChangePasswordWithPhoneScreen extends StatefulWidget {
  final PasswordModifyType type;
  const ChangePasswordWithPhoneScreen({super.key, required this.type});

  @override
  State<ChangePasswordWithPhoneScreen> createState() => _ChangePasswordWithPhoneScreenState();
}

class _ChangePasswordWithPhoneScreenState extends State<ChangePasswordWithPhoneScreen> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verificationCodeController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  bool _isFormValid = false;
  final OtpCubit _otpCubit = OtpCubit();

  late PinTheme defaultPinTheme;

  @override
  void initState() {
    super.initState();
    final currentPhone = context.read<ProfileCubit>().state.userData?.mobile;
    if (currentPhone != null) {
      _phoneController.text = currentPhone;
    }

    for (var controller in [_verificationCodeController, _passwordController, _confirmPasswordController]) {
      controller.addListener(_validateForm);
    }
  }

  void _validateForm() {
    if (widget.type == PasswordModifyType.financial) {
      final codeValid = _verificationCodeController.text.length >= 4;
      final pinValid = _passwordController.text.length == 6;
      setState(() => _isFormValid = codeValid && pinValid);
      return;
    }

    final codeValid = _verificationCodeController.text.length >= 4;
    final passwordValid = Validators.validatePassword(_passwordController.text) == null;
    final confirmPasswordValid = _passwordController.text == _confirmPasswordController.text && passwordValid;
    setState(() => _isFormValid = codeValid && passwordValid && confirmPasswordValid);
  }

  void _handleSendVerificationCode() {
    final mobileValidation = Validators.validateMobile(_phoneController.text);
    if (mobileValidation != null) {
      GPEasyLoading.showToast(mobileValidation);
      return;
    }

    // Store the color for later use to avoid BuildContext across async gap
    final errorColor = context.colorTheme.stockRed;

    GPEasyLoading.showLoading(message: 'sendingCode'.tr());
    _otpCubit.sendOtp(_phoneController.text, type: OtpType.updatePassword).then((_) {
      if (!mounted) return;

      GPEasyLoading.dismiss();

      if (_otpCubit.state.sendStatus == DataStatus.success) {
        GPEasyLoading.showSuccess(message: 'verificationCodeSent'.tr());
      }
    });
  }

  Future<void> _handleSubmit() async {
    if (!_isFormValid) return;

    GPEasyLoading.showLoading();
    final success = await context.read<ProfileCubit>().changePassword(
          password: _passwordController.text,
          smsCode: _verificationCodeController.text,
          type: PasswordChangeType.smsVerification,
          passwordType: widget.type == PasswordModifyType.account ? PasswordType.account : PasswordType.withdrawal,
        );

    GPEasyLoading.dismiss();

    if (success && mounted) {
      GPEasyLoading.showSuccess(message: 'passwordChangedSuccessfully'.tr());
      if (widget.type == PasswordModifyType.account) {
        Helper.logoutUser(context);
      } else {
        Navigator.pop(context);
      }
    } else if (mounted) {
      final error = context.read<ProfileCubit>().state.error;
      final errorColor = context.colorTheme.stockRed;
      GPEasyLoading.showToast(error ?? 'updateFailed'.tr(), bgColor: errorColor);
    }
  }

  void _showKeyboard(TextEditingController controller) {
    final oldPosition = FloatingPosition.bottomRight;

    FloatingWidgetManager().updatePosition(FloatingPosition.centerRight);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.transparent,
      builder: (_) => WithdrawalPasswordKeyboard(
        controller: controller,
        bottomPadding: MediaQuery.of(context).viewInsets.bottom + MediaQuery.of(context).padding.bottom,
        onChanged: (_) => _validateForm(),
        onSubmit: () {
          Navigator.pop(context);
          FocusScope.of(context).unfocus();
        },
      ),
    ).whenComplete(() => FloatingWidgetManager().updatePosition(oldPosition));
  }

  @override
  Widget build(BuildContext context) {
    // Initialize the defaultPinTheme here where context is available
    defaultPinTheme = PinTheme(
      width: 48,
      height: 48,
      textStyle: context.textTheme.primary.w500,
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(10.gr),
        border: Border.all(color: context.theme.primaryColor, width: 0.1),
      ),
    );

    return BlocProvider(
      create: (_) => _otpCubit,
      child: Scaffold(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        appBar: SettingsAppBar(title: 'phoneVerification'.tr()),
        body: SafeArea(
          child: ListView(
            padding: EdgeInsets.symmetric(horizontal: 16.gw),
            children: [
              24.verticalSpace,
              _buildPhoneSection(context),
              24.verticalSpace,
              _buildPasswordSection(context),
              24.verticalSpace,
              CustomMaterialButton(
                buttonText: 'submit'.tr(),
                onPressed: _isFormValid ? _handleSubmit : null,
                isEnabled: _isFormValid,
                height: 48.gh,
                borderRadius: 8.gr,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhoneSection(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.gr),
        border: Border.all(color: context.theme.dividerColor),
        color: context.theme.cardColor,
      ),
      padding: EdgeInsets.all(16.gr),
      child: BlocBuilder<OtpCubit, OtpState>(
        builder: (context, otpState) {
          return OtpField(
            mobileController: _phoneController,
            codeController: _verificationCodeController,
            otpState: otpState,
            onSendCode: _handleSendVerificationCode,
            readOnlyMobile: true,
          );
        },
      ),
    );
  }

  Widget _buildPasswordSection(BuildContext context) {
    if (widget.type == PasswordModifyType.financial) {
      return Container(
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(8.gr),
          border: Border.all(color: context.theme.dividerColor),
        ),
        padding: EdgeInsets.all(16.gr),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${'newPassword'.tr()} *', style: context.textTheme.stockRed.w500),
            16.verticalSpace,
            GestureDetector(
              onTap: () => _showKeyboard(_passwordController),
              child: AbsorbPointer(
                child: Pinput(
                  controller: _passwordController,
                  length: 6,
                  obscureText: true,
                  defaultPinTheme: defaultPinTheme,
                  focusedPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      border: Border.all(color: context.theme.primaryColor, width: 0.8),
                    ),
                  ),
                  errorPinTheme: defaultPinTheme.copyWith(
                    decoration: defaultPinTheme.decoration!.copyWith(
                      border: Border.all(color: context.colorTheme.stockRed),
                    ),
                  ),
                  errorTextStyle: context.textTheme.stockRed.fs12.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        border: Border.all(color: context.theme.dividerColor),
      ),
      padding: EdgeInsets.all(16.gr),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 80.gw,
                child: Text(
                  '${'newPassword'.tr()} *',
                  style: context.textTheme.stockRed.w500,
                ),
              ),
              Expanded(
                child: TextFieldWidget(
                  controller: _passwordController,
                  hintText: 'registerPasswordHint'.tr(),
                  textInputType: TextInputType.visiblePassword,
                  obscureText: true,
                  fillColor: Colors.transparent,
                  borderType: TextFieldBorderType.none,
                  passwordIcon: true,
                ),
              ),
            ],
          ),
          Divider(color: context.theme.dividerColor),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                width: 80.gw,
                child: Text(
                  '${'confirmPassword'.tr()} *',
                  style: context.textTheme.stockRed.w500,
                ),
              ),
              Expanded(
                child: TextFieldWidget(
                  controller: _confirmPasswordController,
                  hintText: 'reenterPassword'.tr(),
                  textInputType: TextInputType.visiblePassword,
                  obscureText: true,
                  fillColor: Colors.transparent,
                  borderType: TextFieldBorderType.none,
                  passwordIcon: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _verificationCodeController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _otpCubit.close();
    super.dispose();
  }
}

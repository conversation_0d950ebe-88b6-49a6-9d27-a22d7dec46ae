import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/screens/settings/widgets/sign_in_streak_card.dart';
import 'package:gp_stock_app/features/profile/screens/settings/widgets/task_tab_bar.dart';
import 'package:gp_stock_app/features/profile/screens/settings/widgets/vip_card.dart';
import 'package:gp_stock_app/features/profile/screens/settings/widgets/vip_upgrade_card.dart';

import '../../logic/mission_center/cubit/mission_activity_cubit.dart';
import '../../logic/vip/vip_cubit.dart';

class MissionCenterScreen extends StatefulWidget {
  final bool isVip;
  const MissionCenterScreen({super.key, this.isVip = false});

  @override
  State<MissionCenterScreen> createState() => _MissionCenterScreenState();
}

class _MissionCenterScreenState extends State<MissionCenterScreen> {
  bool _initialLoadDone = false;

  Future<void> _refreshData(BuildContext context, {bool isManualRefresh = false}) async {
    if (!isManualRefresh && _initialLoadDone) {
      LogI('Skipping automatic refresh');
      return;
    }

    final futures = <Future>[];
    final missionCubit = context.read<MissionActivityCubit>();
    futures.add(missionCubit.getSignInLog());

    if (widget.isVip) {
      final vipCubit = context.read<VipCubit>();
      futures.add(vipCubit.getUserLevelConfig());
      futures.add(vipCubit.getNextUserLevel());
    }

    await Future.wait(futures);
    _initialLoadDone = true;
  }

  @override
  void initState() {
    super.initState();
    _refreshData(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        title: Text(widget.isVip ? 'vipCenter'.tr() : 'missionCenter'.tr()),
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.gw),
          child: RefreshIndicator(
            onRefresh: () => _refreshData(context, isManualRefresh: true),
            child: widget.isVip
                ? const SingleChildScrollView(
                    physics: AlwaysScrollableScrollPhysics(),
                    child: Column(
                      spacing: 16,
                      children: [
                        VipCard(),
                        VipUpgradeCard(),
                      ],
                    ),
                  )
                : const SingleChildScrollView(
                    child: Column(
                      spacing: 16,
                      children: [
                        SignInStreakCard(),
                        TaskTabBar(),
                      ],
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}

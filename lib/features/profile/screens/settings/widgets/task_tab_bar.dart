import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/activity/logic/activity/activity_cubit.dart';

import '../../../../../shared/app/extension/helper.dart';
import '../../../../../shared/constants/enums.dart';

import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import '../../../../../shared/widgets/shimmer/shimmer_widget.dart';
import '../../../../activity/domain/models/tasks/tasks.dart';
import '../../../../activity/screens/activity_screen.dart';
import '../../../../activity/widgets/tasks/tasks_card.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class TaskTabBar extends StatefulWidget {
  const TaskTabBar({super.key});

  @override
  State<TaskTabBar> createState() => _TaskTabBarState();
}

class _TaskTabBarState extends State<TaskTabBar> {
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    Helper.afterInit(_initialFunction);
  }

  Future<void> _initialFunction() async {
    context.read<ActivityCubit>().getTasks();
  }

  void _onTabSelected(int index) {
    setState(() {
      _selectedTabIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ActivityCubit, ActivityState>(
      builder: (context, state) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTabSelector(),
            10.verticalSpace,
            Container(
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(12.gr),
              ),
              child: state.tasksFetchStatus == DataStatus.loading ? const TaskListShimmer() : _buildTaskContent(state),
            ),
            if (_shouldShowMoreButton(state)) ...[
              16.verticalSpace,
              _buildViewAllButton(),
            ],
          ],
        );
      },
    );
  }

  Widget _buildTabSelector() {
    return Container(
      height: 36.gh,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(6.gr),
      ),
      child: Row(
        children: [
          _buildTabButton(0, 'dailyTask'.tr()),
          _buildTabButton(1, 'tradingTask'.tr()),
          _buildTabButton(2, 'newbieTask'.tr()),
        ],
      ),
    );
  }

  Widget _buildTabButton(int index, String title) {
    final isSelected = _selectedTabIndex == index;
    return Expanded(
      child: GestureDetector(
        onTap: () => _onTabSelected(index),
        child: Container(
          height: 36.gh,
          alignment: Alignment.center,
          decoration: isSelected
              ? BoxDecoration(
                  color: context.theme.primaryColor,
                  borderRadius: BorderRadius.circular(6.gr),
                )
              : null,
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: (isSelected ? context.textTheme.primary.w600 : context.textTheme.regular).copyWith(
              color: isSelected ? Colors.white : context.colorTheme.textRegular,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTaskContent(ActivityState state) {
    // Use IndexedStack to maintain all three lists but only show the selected one
    // This preserves the state and size of each list
    return IndexedStack(
      index: _selectedTabIndex,
      sizing: StackFit.loose, // Allow the stack to size to its children
      children: [
        _buildTaskList(state.dailyTasks ?? []),
        _buildTaskList(state.tradeTasks ?? []),
        _buildTaskList(state.newUserTasks ?? []),
      ],
    );
  }

  Widget _buildTaskList(List<TaskEntity> tasks) {
    if (tasks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assignment_outlined,
              size: 48.gw,
              color: context.colorTheme.textRegular.withValues(alpha: 128),
            ),
            8.verticalSpace,
            Text(
              'noTasksAvailable'.tr(),
              style: context.textTheme.regular,
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: EdgeInsets.all(16.gr),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: tasks.length,
      separatorBuilder: (context, index) => 12.verticalSpace,
      itemBuilder: (context, index) {
        return TaskItemCard(
          task: tasks[index],
          // Removed Task Transformer functions for now
        );
      },
    );
  }

  bool _shouldShowMoreButton(ActivityState state) {
    if (state.tasksFetchStatus != DataStatus.success) return false;

    final currentList = _selectedTabIndex == 0
        ? state.newUserTasks
        : _selectedTabIndex == 1
            ? state.tradeTasks
            : state.dailyTasks;

    return (currentList?.length ?? 0) > 3;
  }

  Widget _buildViewAllButton() {
    return CommonButton(
      style: CommonButtonStyle.outlined,
      height: 36.gh,
      title: 'viewAll'.tr(),
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const ActivityScreen(showBackButton: true),
          ),
        ).then((_) {
          if (!mounted) return;
          context.read<ActivityCubit>().getTasks();
        });
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'viewAll'.tr(),
            style: context.textTheme.regular,
          ),
          Icon(
            Icons.chevron_right,
            size: 16.gw,
            color: context.colorTheme.textRegular,
          ),
        ],
      ),
    );
  }
}

class TaskListShimmer extends StatelessWidget {
  const TaskListShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: EdgeInsets.all(8.gw),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 2,
      separatorBuilder: (_, __) => 8.verticalSpace,
      itemBuilder: (_, __) => const TaskItemShimmer(),
    );
  }
}

class TaskItemShimmer extends StatelessWidget {
  const TaskItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      child: Container(
        padding: EdgeInsets.all(16.gw),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.gr),
          color: Colors.white,
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 120.gw,
                  height: 16.gh,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                ),
                Container(
                  width: 60.gw,
                  height: 16.gh,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                ),
              ],
            ),
            12.verticalSpace,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: 80.gw,
                  height: 14.gh,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                ),
                Container(
                  width: 100.gw,
                  height: 14.gh,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(4.gr),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

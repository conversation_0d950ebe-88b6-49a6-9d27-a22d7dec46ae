import 'dart:io';

import 'package:dio/dio.dart';
import 'package:gp_stock_app/features/profile/domain/models/app_info/app_info_model.dart';
import 'package:gp_stock_app/features/sign_in/domain/models/login/login_response.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../../../../shared/constants/enums.dart';
import '../models/auth_n/file/file_upload.dart';
import '../models/auth_n/info/auth_n_info.dart';
import '../repository/profile_repository.dart';

@Injectable(as: ProfileRepository)
class ProfileService implements ProfileRepository {
  @override
  Future<ResponseResult<bool>> authApply({
    required String? certificateBack,
    required String? certificateFront,
    required int? certificateType,
    required String? idCard,
    required String? realName,
    required String withdrawPassword,
    required String? bankMobile,
    required String? bankCardNo,
  }) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.authNApply,
        options: Options(headers: {'auth': true}),
        data: {
          'certificateBack': certificateBack,
          'certificateFront': certificateFront,
          'certificateType': certificateType,
          'idCard': idCard,
          'realName': realName,
          'withdrawPassword': withdrawPassword,
          'bankMobile': bankMobile,
          'bankCardNo': bankCardNo,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to logout');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<FileUpload>> fileUpload({required File? file, required String? type}) async {
    try {
      final multipartFile = await MultipartFile.fromFile(file!.path, filename: file.path.split('/').last);
      final Response response = await NetworkProvider().formData(
        ApiEndpoints.uploadImage,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryy2TfwtOnYLAHfYlM',
            'auth': true,
          },
        ),
        formData: FormData.fromMap({'file': multipartFile}),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: FileUpload.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to upload profile image');
      }
    } on Error catch (e) {
      return ResponseResult(error: e.toString());
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<AuthNInfo>> authNInfo() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.authNInfo,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: AuthNInfo.fromJson(response.data['data'] ?? {}));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get authN info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> updateUserInfo({
    String? avatar,
    String? nickname,
    int? sex,
    String? email,
    String? phoneNo,
  }) async {
    try {
      final Map<String, dynamic> data = {};

      // Only add fields that are being updated
      if (avatar != null) data['avatar'] = avatar;
      if (nickname != null) data['nickname'] = nickname;
      if (sex != null) data['sex'] = sex;
      if (email != null) data['email'] = email;
      if (phoneNo != null) data['bio'] = phoneNo;

      final Response response = await NetworkProvider().put(
        ApiEndpoints.updateUserInfo,
        data: data,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to update user info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> changePassword({
    String? mobile,
    String? password,
    String? newPassword,
    String? smsCode,
    required String verifyType,
    PasswordType? passwordType,
  }) async {
    try {
      Map<String, dynamic> requestData = {
        if (passwordType != null) 'type': passwordType.value,
      };

      if (verifyType == "mobile") {
        requestData.addAll({
          'mobile': mobile,
          'newPassword': newPassword,
          'smsCode': smsCode,
          'verifyType': verifyType,
        });
      } else if (verifyType == "account") {
        requestData.addAll({
          'password': password,
          'newPassword': newPassword,
          'verifyType': verifyType,
        });
      }

      final Response response = await NetworkProvider().post(
        ApiEndpoints.changePassword,
        data: requestData,
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        }
        return ResponseResult(error: response.data['msg']);
      }
      return ResponseResult(error: 'Failed to change password');
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> updateMobile({
    required String countryCode,
    required String currentMobile,
    required String newMobile,
    required String smsCode,
  }) async {
    try {
      final Response response = await NetworkProvider().put(
        ApiEndpoints.updateMobile,
        data: {
          'countryCode': countryCode,
          'currentMobile': currentMobile,
          'newMobile': newMobile,
          'smsCode': smsCode,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        }
        return ResponseResult(error: response.data['msg']);
      }
      return ResponseResult(error: 'Failed to update mobile number');
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }



  @override
  Future<ResponseResult<List<AppInfoModel>>> getAppInfoList() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.appInfoList,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final List<dynamic> data = response.data['data'] ?? [];
          return ResponseResult(
            data: data.map((e) => AppInfoModel.fromJson(e)).toList(),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get app info list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

}

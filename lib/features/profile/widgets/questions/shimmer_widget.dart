import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class QuestionShimmerWidget extends StatelessWidget {
  const QuestionShimmerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: 3, // Show 3 shimmer categories
      itemBuilder: (context, index) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 12.gh),
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.gr),
                  topRight: Radius.circular(8.gr),
                ),
              ),
              child: ShimmerWidget(
                height: 20.gh,
                width: 150.gw,
              ),
            ),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 3, // Show 3 shimmer items per category
              separatorBuilder: (context, index) => Divider(
                color: context.theme.cardColor,
                height: 1,
                thickness: 0.1,
              ),
              itemBuilder: (context, qIndex) {
                return Container(
                  decoration: BoxDecoration(
                    color: context.theme.cardColor,
                    borderRadius: qIndex == 2
                        ? BorderRadius.only(
                            bottomLeft: Radius.circular(8.gr),
                            bottomRight: Radius.circular(8.gr),
                          )
                        : null,
                  ),
                  child: ListTile(
                    title: ShimmerWidget(
                      height: 16.gh,
                      width: double.infinity,
                    ),
                    trailing: Icon(
                      Icons.chevron_right,
                      color: context.colorTheme.textRegular.withNewOpacity(0.3),
                      size: 24.gr,
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12.gw),
                  ),
                );
              },
            ),
            12.verticalSpace,
          ],
        );
      },
    );
  }
}

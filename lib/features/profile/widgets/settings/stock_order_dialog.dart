import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/sort_color/sort_color_cubit.dart';

import '../../../../shared/constants/assets.dart';
import '../../../../shared/widgets/alert_dilaog/dialog_helper.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class StockOrderDialog extends StatelessWidget {
  const StockOrderDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.gr)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DialogHeader(title: 'priceColor'.tr()),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gh),
              child: BlocSelector<SortColorCubit, SortColorState, MarketColor>(
                selector: (state) => state.marketColor,
                builder: (context, state) {
                  return Column(
                    children: [
                      ListTile(
                        onTap: () {
                          context.read<SortColorCubit>().toggleMarketColor(MarketColor.redUpGreenDown);
                          Navigator.pop(context);
                        },
                        contentPadding: EdgeInsets.symmetric(horizontal: 8.gw),
                        leading: SvgPicture.asset(
                          Assets.greenDownIcon,
                          height: 24.gr,
                          width: 24.gr,
                        ),
                        title: Text(
                          'redUpGreenDown'.tr(),
                          style: context.textTheme.primary.w500,
                        ),
                        trailing: state == MarketColor.redUpGreenDown
                            ? Icon(
                                Icons.check_circle,
                                color: context.theme.primaryColor,
                                size: 20.gr,
                              )
                            : null,
                        selected: state == MarketColor.redUpGreenDown,
                        selectedTileColor: context.theme.primaryColor.withValues(alpha: 0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.gr),
                        ),
                      ),
                      8.verticalSpace,
                      ListTile(
                        onTap: () {
                          context.read<SortColorCubit>().toggleMarketColor(MarketColor.greenUpRedDown);
                          Navigator.pop(context);
                        },
                        contentPadding: EdgeInsets.symmetric(horizontal: 8.gw),
                        leading: SvgPicture.asset(
                          Assets.redDownIcon,
                          height: 24.gr,
                          width: 24.gr,
                        ),
                        title: Text(
                          'greenUpRedDown'.tr(),
                          style: context.textTheme.primary.w500,
                        ),
                        trailing: state == MarketColor.greenUpRedDown
                            ? Icon(
                                Icons.check_circle,
                                color: context.theme.primaryColor,
                                size: 20.gr,
                              )
                            : null,
                        selected: state == MarketColor.greenUpRedDown,
                        selectedTileColor: context.theme.primaryColor.withValues(alpha: 0.1),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.gr),
                        ),
                      ),
                      DialogFooter(),
                      16.verticalSpace,
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

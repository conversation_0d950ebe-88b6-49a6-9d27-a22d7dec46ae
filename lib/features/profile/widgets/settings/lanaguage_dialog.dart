import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../../shared/app/extension/helper.dart';

import '../../../../shared/widgets/alert_dilaog/dialog_helper.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class LanguageDialog extends StatelessWidget {
  const LanguageDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final currentLocale = context.locale;

    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.gr)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DialogHeader(title: 'language'.tr()),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 8.gh),
              child: Column(
                children: context.supportedLocales.map((locale) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 8.gh),
                    child: ListTile(
                      contentPadding: EdgeInsets.symmetric(horizontal: 8.gw),
                      onTap: () async {
                        HapticFeedback.lightImpact();
                        try {
                          if (context.supportedLocales.contains(locale)) {
                            await context.setLocale(locale);
                            if (context.mounted) {
                              Navigator.pop(context);
                            }
                          }
                        } catch (e) {
                          debugPrint('Error setting locale: $e');
                        }
                      },
                      leading: SvgPicture.asset(
                        Helper().getLanguageIcon(locale),
                        width: 24.gw,
                        height: 24.gh,
                        colorFilter: ColorFilter.mode(context.theme.primaryColor, BlendMode.srcIn),
                      ),
                      title: Text(
                        Helper().getLanguageName(locale),
                        style: context.textTheme.primary.w500,
                      ),
                      trailing: locale == currentLocale
                          ? Icon(
                              Icons.check_circle,
                              color: context.theme.primaryColor,
                              size: 20.gr,
                            )
                          : null,
                      selected: currentLocale == locale,
                      selectedTileColor: context.theme.primaryColor.withValues(alpha: 0.1),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.gr),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            DialogFooter(),
            16.verticalSpace,
          ],
        ),
      ),
    );
  }
}

import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/models/entities/account.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/account/domain/models/account_info/account_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/calculate_config/calculate_config.dart';
import 'package:gp_stock_app/features/account/domain/services/account_service.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';

class FTradeService {
  /// 期货单独证劵配置信息
  static Future<FTradeConfigModel?> fetchFTradeConfig({required String instrument}) async {
    String market = '';
    String securityType = '';
    String symbol = '';
    if (instrument.isNotEmpty && instrument.contains('|')) {
      List<String> parts = instrument.split('|');
      if (parts.length >= 3) {
        market = parts[0];
        securityType = parts[1];
        symbol = parts[2];
      } else {
        assert(false, 'Error instrument');
      }
    }
    final response = await Http().request<FTradeConfigModel>(
      ApiEndpoints.getFuturesGetConfig,
      method: HttpMethod.get,
      queryParameters: {'market': market, 'securityType': securityType, 'symbol': symbol},
    );
    return response.data;
  }

  static Future<double?> fetchUsableCash() async {
    final response = await Http().request<AccountInfo>(
      ApiEndpoints.getAccountInfo,
      method: HttpMethod.get,
    );
    return response.data?.usableCash;
  }

  // 2：交易中 4：已收盘 6：交易时段间休市 0：待开盘
  static Future<FTradeStateModel?> fetchTradeState(String market, String productCode) async {
    final response = await Http().request<FTradeStateModel>(
      ApiEndpoints.getFuturesMarketStatus,
      method: HttpMethod.get,
      queryParameters: {'market': market, 'productCode': productCode},
    );
    return response.data;
  }

  static Future<(List<CalculateConfig>, List<CalculateConfig>)> fetchServeFeeCalculateConfig({
    required String instrument,
  }) async {
    String market = '';
    String securityType = '';
    if (instrument.isNotEmpty && instrument.contains('|')) {
      List<String> parts = instrument.split('|');
      if (parts.length >= 3) {
        market = parts[0];
        securityType = parts[1];
      }
    }
    // 1=>buy 2=>sell
    try {
      final results = await Future.wait([
        AccountService().getCalculateConfig(market: market, direction: "1", securityType: securityType),
        AccountService().getCalculateConfig(market: market, direction: "2", securityType: securityType)
      ]);

      if (results.every((e) => e.isSuccess && e.data != null)) {
        return (results[0].data ?? [], results[1].data ?? []);
      }
    } catch (_) {}
    return (<CalculateConfig>[], <CalculateConfig>[]);
  }
}

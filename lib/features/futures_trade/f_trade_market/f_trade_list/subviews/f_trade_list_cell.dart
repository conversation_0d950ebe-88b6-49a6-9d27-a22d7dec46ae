import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';

import 'package:gp_stock_app/shared/widgets/flip_text.dart';

class FTradeListCell extends StatelessWidget {
  final FTradeListItemModel data;
  final void Function(FTradeListItemModel) onTap;

  const FTradeListCell({
    super.key,
    required this.data,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => onTap(data),
        borderRadius: BorderRadius.circular(8.gr),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 12.gh),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildNameAndSymbol(context),
              Expanded(
                flex: 4,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 2,
                      child: Padding(
                        padding: EdgeInsets.only(right: 20.gw),
                        child: _buildLatestPrice(context),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: _buildChangePercentage(context),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNameAndSymbol(BuildContext context) {
    return Expanded(
      flex: 3,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            data.name,
            style: context.textTheme.primary.copyWith(overflow: TextOverflow.ellipsis),
            maxLines: 3,
          ),
          4.verticalSpace,
          Text(
            data.symbol.toUpperCase(),
            style: context.textTheme.regular.fs12,
          ),
        ],
      ),
    );
  }

  Widget _buildLatestPrice(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: FlipText(
        data.latestPrice,
        fractionDigits: 3,
        style: context.textTheme.primary.ffAkz,
      ),
    );
  }

  Widget _buildChangePercentage(BuildContext context) {
    final gainValue = data.gain * 100;
    final isPositive = gainValue > 0;

    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        constraints: BoxConstraints(
          minWidth: 60.gw, // Minimum width to ensure consistency
          maxWidth: 80.gw, // Maximum width to prevent overflow
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 8.gw,
          vertical: 6.gh,
        ),
        decoration: BoxDecoration(
          color: data.gain.getValueColor(context),
          borderRadius: BorderRadius.circular(4.gr),
        ),
        child: Center(
          child: FlipText(
            gainValue.abs(),
            prefix: isPositive ? '+' : '-',
            suffix: '%',
            style: context.textTheme.primary.w700.ffAkz.copyWith(
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:country_flags/country_flags.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/stock_info_item.dart';
import 'package:gp_stock_app/features/account/utils/trading_utils.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';

import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FTradeKLineHeaderTopView extends StatelessWidget {
  final FTradeInfoModel data;
  final FTradeStateModel? stateData;
  const FTradeKLineHeaderTopView({super.key, required this.data, this.stateData});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(12, 8, 12, 0),
      margin: EdgeInsets.symmetric(vertical: 0, horizontal: 14),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMarketStatus(context),
          const SizedBox(height: 4),
          _buildStockDetails(context),
        ],
      ),
    );
  }

  /// Builds the market status and timestamp row.
  Widget _buildMarketStatus(BuildContext context) {
    return Row(
      children: [
        Text(
          stateData?.statusStr ?? '',
          style: context.textTheme.primary.fs12.copyWith(color: Color(0xff8897B8)),
        ),
        const SizedBox(width: 8),
        // TODO: 三方数据不稳定 暂时不显示
        Text(
          '', //stateData?.timestampDisplay(timestamp: data.latestTime, market: data.market) ?? '',
          style: context.textTheme.regular.fs12,
        ),
      ],
    );
  }

  /// Builds the stock price, gain, and name section.
  Widget _buildStockDetails(BuildContext context) {
    final chg = TradingUtils.formatNumber(data.chg);
    final gain = TradingUtils.formatPercentage(data.gain * 100, isPercent: false, decimalPlaces: 3);
    final name = data.name;
    final gainColor = data.gain.getValueColor(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 4,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  FlipText(
                    data.latestPrice,
                    style: context.textTheme.primary.fs20.w700.copyWith(color: gainColor),
                    fractionDigits: 3,
                  ),
                  Icon(
                    data.gain >= 0 ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                    color: gainColor,
                  ),
                ],
              ),
              Text(
                "${TradingUtils.getSign(data.gain)}$chg  ${TradingUtils.getSign(data.gain)}$gain %",
                style: context.textTheme.primary.fs13.w700.copyWith(color: gainColor),
              ),
            ],
          ),
        ),
        Expanded(
          flex: 6,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end, // Align content to the right
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Flexible(
                child: Text(
                  name,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: context.textTheme.primary,
                ),
              ),
              const SizedBox(width: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(2),
                child: CountryFlag.fromCountryCode(
                  'cn',
                  width: 20,
                  height: 15,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class FTradeKLineHeaderBottomView extends StatefulWidget {
  final FTradeInfoModel? data;
  const FTradeKLineHeaderBottomView({super.key, required this.data});

  @override
  State<FTradeKLineHeaderBottomView> createState() => _FTradeKLineHeaderBottomViewState();
}

class _FTradeKLineHeaderBottomViewState extends State<FTradeKLineHeaderBottomView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(12, 0, 12, 8),
      margin: EdgeInsets.fromLTRB(14, 0, 14, 10),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0x14354677), // #35467714，8%
            offset: Offset(0, 4),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 1,
            color: context.theme.dividerColor,
          ),
          _buildStockInfo(context),
        ],
      ),
    );
  }

  /// Builds the main stock info section (always visible).
  Widget _buildStockInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInfoRow(
          context,
          left: StockInfoItem("high_price".tr(), widget.data?.high ?? 0.0,
              color: context.textTheme.stockRed.color!, isPrice: true, precision: 3),
          right: StockInfoItem("low_price".tr(), widget.data?.low ?? 0.0,
              color: context.textTheme.stockGreen.color!, isPrice: true, precision: 3),
        ),
        const SizedBox(height: 12),
        _buildInfoRow(
          context,
          left: StockInfoItem("opening_price".tr(), widget.data?.open ?? 0.0,
              color: context.textTheme.stockGreen.color!, isPrice: true, precision: 3),
          right: StockInfoItem("prev_close".tr(), widget.data?.close ?? 0.0,
              color: context.textTheme.primary.color!, isPrice: true, precision: 3),
        ),
        const SizedBox(height: 12),
        _buildInfoRow(
          context,
          left: StockInfoItem("turnover_amount".tr(), widget.data?.amount ?? 0.0,
              color: context.textTheme.primary.color!, isVolume: true),
          right: StockInfoItem("volume".tr(), widget.data?.volume ?? 0,
              color: context.textTheme.primary.color!, isVolume: true, isBold: true),
        ),
      ],
    );
  }

  /// Builds a row with two stock info items.
  Widget _buildInfoRow(BuildContext context, {required Widget left, required Widget right}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(child: left),
        const SizedBox(width: 12),
        Expanded(child: right),
      ],
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_config_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_service.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_scroll_service.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 't_trade_profile_scroll_state.dart';

class FTradeProfileScrollCubit extends Cubit<FTradeProfileScrollState> {
  FTradeProfileScrollCubit() : super(FTradeProfileScrollState());

  Future<void> fetchData({
    required String instrument,
  }) async {
    try {
      emit(state.copyWith(
        status: DataStatus.loading,
      ));
      final results = await Future.wait([
        FTradeKLineScrollService.fetchFTradeInfo(null, instrument: instrument),
        FTradeService.fetchFTradeConfig(instrument: instrument),
      ]);

      final result0 = results[0];
      FTradeInfoModel? tradeInfo = result0 is FTradeInfoModel ? result0 : null;
      final result1 = results[1];
      FTradeConfigModel? tradeConfig = result1 is FTradeConfigModel ? result1 : null;

      if (tradeInfo != null && tradeConfig != null) {
        emit(state.copyWith(
          status: DataStatus.success,
          tradeInfo: tradeInfo,
          tradeConfig: tradeConfig,
        ));
      } else {
        emit(state.copyWith(
          status: DataStatus.failed,
        ));
        Helper.showFlutterToast(
          'Network data error',
        );
      }
    } catch (e) {
      emit(state.copyWith(
        status: DataStatus.failed,
      ));
      if (kDebugMode) {
        Helper.showFlutterToast(
          'Failed to fetch network',
        );
      }
    }
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/counter_textfield.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class FTradeFromLimitView extends StatefulWidget {
  /// A widget that handles trade limit price input and display.
  ///
  /// This widget provides:
  /// - Real-time price display for market orders
  /// - Limit price input with increment/decrement controls
  /// - Automatic price rounding to 2 decimal places
  /// - Focus management for text input
  const FTradeFromLimitView({
    super.key,
    required this.priceType,
    required this.maxLimit,
    required this.minLimit,
    required this.latestPrice,
    required this.canDecrement,
    required this.canIncrement,
    required this.decimalPlaces,
    required this.onDecrement,
    required this.onIncrement,
    required this.onLimitChanged,
  });

  final PriceType priceType;
  final double maxLimit;
  final double minLimit;
  final double latestPrice;
  final bool canDecrement;
  final bool canIncrement;
  final int decimalPlaces;
  final VoidCallback? onDecrement;
  final VoidCallback onIncrement;
  final ValueChanged<double> onLimitChanged;

  @override
  State<FTradeFromLimitView> createState() => _FTradeFromLimitViewState();
}

class _FTradeFromLimitViewState extends State<FTradeFromLimitView> {
  final TextEditingController _limitController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  /// The multiplier used for rounding calculations
  final int _roundingMultiplier = 100;

  @override
  void initState() {
    super.initState();
    double displayPrice = widget.latestPrice < widget.minLimit ? widget.minLimit : widget.latestPrice;
    displayPrice = widget.latestPrice > widget.maxLimit ? widget.maxLimit : widget.latestPrice;
    _limitController.text = displayPrice.toStringAsFixed(widget.decimalPlaces);
  }

  @override
  void dispose() {
    _limitController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.priceType == PriceType.market) {
      return _buildRealTimePriceLabelDisplay();
    }

    return Expanded(
      child: CounterTextfield(
        onDecrementPressed: widget.canDecrement ? widget.onDecrement : null,
        onIncrementPressed: widget.canIncrement ? widget.onIncrement : null,
        controller: _limitController,
        focusNode: _focusNode,
        onFocusChanged: () {
          final currentValue = double.tryParse(_limitController.text) ?? widget.maxLimit;
          _updateLimit(currentValue);
        },
      ),
    );
  }

  void _updateLimit(double price) {
    final roundedPrice = _roundPrice(price);
    double finalPrice = 0;
    if (roundedPrice < widget.minLimit) {
      finalPrice = widget.minLimit;
    } else {
      finalPrice = roundedPrice == 0 ? widget.latestPrice : roundedPrice;
    }
    if (roundedPrice > widget.maxLimit) {
      finalPrice = widget.maxLimit;
    } else {
      finalPrice = roundedPrice == 0 ? widget.latestPrice : roundedPrice;
    }
    _limitController.text = finalPrice.toStringAsFixed(widget.decimalPlaces);
    widget.onLimitChanged(finalPrice);
  }

  double _roundPrice(double price) {
    return (price * _roundingMultiplier).round() / _roundingMultiplier;
  }

  Widget _buildRealTimePriceLabelDisplay() {
    return Container(
      height: 35.gh,
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(5.gr),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'realTimePrice'.tr(),
            style: context.textTheme.primary.fs13,
          ),
        ],
      ),
    );
  }
}

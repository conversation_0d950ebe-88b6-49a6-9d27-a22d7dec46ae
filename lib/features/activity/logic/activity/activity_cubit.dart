import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/task.dart';
import 'package:gp_stock_app/core/models/entities/task/task_center_response_entity.dart';

import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/app/extension/helper.dart';
import '../../../../shared/constants/enums.dart';

part 'activity_state.dart';

@injectable
class ActivityCubit extends AuthAwareCubit<ActivityState> {
  ActivityCubit() : super(const ActivityState());

  @override
  void onLoggedIn() => getTasks();

  @override
  void onLoggedOut() => emit(const ActivityState());

  void updateTab(int index) => emit(state.copyWith(selectedTab: index));

  void updateSelectedTab(int index) => emit(state.copyWith(selectedEventIndex: index));

  Future<void> getTasks() async {
    emit(state.copyWith(tasksFetchStatus: DataStatus.loading));

    final res = await TaskApi.fetchTaskCenterData();
    if (res != null) {
      emit(state.copyWith(
        tasksFetchStatus: DataStatus.success,
        newUserTasks: res.newUser,
        tradeTasks: res.trade,
        dailyTasks: res.daily,
        error: null,
      ));
    } else {
      emit(state.copyWith(tasksFetchStatus: DataStatus.failed));
    }
  }

  Future<void> collectReward(TaskEntity task) async {
    if (task.isCompleted != true || task.isReceived == true) return;
    if (task.id == 0) return;

    emit(state.copyWith(rewardCollectionStatus: DataStatus.loading));

    final result = await TaskApi.collectReward(task.id);

    if (result != null) {
      Helper.showFlutterToast(
        'rewardCollected'.tr(),
      );
      await getTasks();
      emit(state.copyWith(rewardCollectionStatus: DataStatus.success));
    } else {
      emit(state.copyWith(rewardCollectionStatus: DataStatus.failed, error: 'Failed to collect reward'));
      Helper.showFlutterToast(
        'Failed to collect reward',
      );
    }
  }
}

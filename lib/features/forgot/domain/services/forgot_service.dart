import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class ForgotService {
  static Future<bool> resetPassword({
    required String mobile,
    required String newPassword,
    required String smsCode,
    required String verifyType,
    required PasswordType passwordType,
  }) async {
    try {
      final Map<String, dynamic> requestData = {
        'mobile': mobile,
        'newPassword': newPassword,
        'smsCode': smsCode,
        'verifyType': verifyType,
        'type': passwordType.value,
      };

      final response = await Http().request<bool>(
        ApiEndpoints.changePassword,
        method: HttpMethod.post,
        params: requestData,
        needSignIn: false,
      );

      return response.isSuccess;
    } catch (e) {
      return false;
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

import '../../../../shared/constants/enums.dart';
import '../../../../shared/routes/routes.dart';
import '../../logic/news/news_cubit.dart';
import '../../logic/news/news_state.dart';
import 'news_content.dart';
import 'news_image.dart';

class NewsList extends StatelessWidget {
  const NewsList({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<NewsCubit, NewsState>(
      builder: (context, state) {
        if (state.status == DataStatus.loading) {
          return const NewsListShimmer();
        }

        if (state.status == DataStatus.failed) {
          return Center(child: TableEmptyWidget(height: 50.gh, width: 50.gw));
        }

        final news = state.newsData?.records ?? [];
        if (news.isEmpty) {
          return const Center(child: Text('No news available'));
        }

        return AnimationLimiter(
          child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(),
            itemCount: news.length,
            shrinkWrap: true,
            separatorBuilder: (_, __) => Divider(
              height: 24.gh,
              thickness: 1,
              color: context.theme.primaryColor.withValues(alpha: 0.05),
            ),
            itemBuilder: (context, index) {
              final item = news[index];
              return AnimationConfiguration.staggeredList(
                position: index,
                duration: const Duration(milliseconds: 600),
                child: SlideAnimation(
                  verticalOffset: 30.0,
                  child: FadeInAnimation(
                    child: Bounceable(
                      onTap: () => Navigator.pushNamed(
                        context,
                        routeNewsDetails,
                        arguments: item,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Hero(
                            tag: item.id,
                            child: NewsImage(imageUrl: item.url),
                          ),
                          12.horizontalSpace,
                          Expanded(
                            child: NewsContent(
                              news: item,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

class NewsListItemShimmer extends StatelessWidget {
  const NewsListItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.gh),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Shimmer for Image
            Container(
              width: 120.gw,
              height: 80.gh,
              decoration: BoxDecoration(
                color: context.theme.cardColor,
                borderRadius: BorderRadius.circular(8.gr),
              ),
            ),
            16.horizontalSpace,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Shimmer for Title
                  Container(
                    width: double.infinity,
                    height: 16.gh,
                    color: context.theme.cardColor,
                    margin: EdgeInsets.only(bottom: 8.gh),
                  ),
                  // Shimmer for Second Title Line
                  Container(
                    width: 0.7.gsw,
                    height: 16.gh,
                    color: context.theme.cardColor,
                    margin: EdgeInsets.only(bottom: 8.gh),
                  ),
                  // Shimmer for Date
                  Container(
                    width: 100.gw,
                    height: 12.gh,
                    color: context.theme.cardColor,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NewsListShimmer extends StatelessWidget {
  final int itemCount;

  const NewsListShimmer({
    super.key,
    this.itemCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: itemCount,
      separatorBuilder: (_, __) => Divider(
        height: 24.gh,
        thickness: 1,
        color: context.theme.primaryColor.withValues(alpha: 0.05),
      ),
      itemBuilder: (context, index) => const NewsListItemShimmer(),
    );
  }
}

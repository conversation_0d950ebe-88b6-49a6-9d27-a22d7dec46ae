import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../domain/models/news/news_model.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class NewsDetailsScreen extends StatelessWidget {
  final NewsItem news;

  const NewsDetailsScreen({
    super.key,
    required this.news,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('newsDetails'.tr()),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.gw),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              14.verticalSpace,
              // Title
              Text(
                news.title,
                style: AppConfig.instance.skinStyle == AppSkinStyle.kTemplateA
                    ? context.textTheme.primary.fs16.w800
                    : context.textTheme.title.fs16.w800,
              ),
              16.verticalSpace,
              // Author and Time
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    news.comefrom,
                    style: context.textTheme.regular.fs12,
                  ),
                  Text(
                    news.publishTime,
                    style: context.textTheme.regular.fs12,
                  ),
                ],
              ),
              12.verticalSpace,
              Text(
                news.digest,
                style: AppConfig.instance.skinStyle == AppSkinStyle.kTemplateA
                    ? context.textTheme.primary
                    : context.textTheme.title,
              ),
              12.verticalSpace,

              // Image
              if (news.url.isNotEmpty)
                Hero(
                  tag: news.id,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.gr),
                    child: Container(
                      color: Colors.grey[200],
                      child: Image.network(
                        news.url,
                        width: double.infinity,
                        height: 200.gh,
                        fit: BoxFit.cover,
                        errorBuilder: (_, __, ___) => Container(
                          width: double.infinity,
                          height: 200.gh,
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(8.gr),
                          ),
                          child: Icon(
                            Icons.image_not_supported,
                            size: 40.gw,
                            color: Colors.grey[400],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              16.verticalSpace,

              // Content
              Text(
                news.content,
                style: context.textTheme.regular.fs12.copyWith(
                  color: context.colorTheme.textRegular,
                  height: 1.5,
                ),
              ),

              //
              SizedBox(height: MediaQuery.of(context).padding.bottom),
            ],
          ),
        ),
      ),
    );
  }
}

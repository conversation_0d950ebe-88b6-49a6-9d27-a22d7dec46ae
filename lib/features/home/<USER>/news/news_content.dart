import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../domain/models/news/news_model.dart';

class NewsContent extends StatelessWidget {
  final NewsItem news;

  const NewsContent({
    super.key,
    required this.news,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 80.gh,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              news.title,
              maxLines: 2,
              style: context.textTheme.regular.w600,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          DefaultTextStyle(
            style: context.textTheme.regular.fs12,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(news.comefrom),
                Text(news.publishTime),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

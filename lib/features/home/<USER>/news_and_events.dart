import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/news/news_list.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/widgets/market_table_header_style_d.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

import '../../market/widgets/market_table_header.dart';
import '../logic/home/<USER>';

class NewsAndEvents extends StatelessWidget {
  const NewsAndEvents({super.key});

  @override
  Widget build(BuildContext context) {
    return ShadowBox(
      borderRadius: getBorderRadius(context),
      child: BlocBuilder<HomeCubit, HomeState>(
        builder: (context, state) {
          return Column(
            children: [
              Row(
                children: [
                  getMarketTableHeader(
                    context,
                    title: "news".tr(),
                    isSelected: state.selectedNewsTab == 0,
                    onTap: () => context.read<HomeCubit>().updateNewsTab(0),
                  ),
                  Spacer(),
                  TextButton.icon(
                    icon: const Icon(Icons.chevron_right_outlined),
                    iconAlignment: IconAlignment.end,
                    onPressed: () => context.read<MainCubit>().selectedNavigationItem(NavigationItem.activity),
                    label: Text("more".tr()),
                    style: TextButton.styleFrom(
                      foregroundColor: context.colorTheme.textRegular,
                      iconColor: context.colorTheme.textRegular,
                      padding: EdgeInsets.symmetric(horizontal: 4.gh, vertical: 4.gh),
                      minimumSize: const Size(0, 0),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ],
              ),
              10.verticalSpace,
              // Display news and events
              if (state.selectedNewsTab == 0) NewsList(),
              10.verticalSpace,
            ],
          );
        },
      ),
    );
  }

  BorderRadius? getBorderRadius(BuildContext context) {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateD => BorderRadius.circular(0),
      _ => null,
    };
  }
}

Widget getMarketTableHeader(BuildContext context,
    {required String title, required bool isSelected, required Function() onTap}) {
  return switch (AppConfig.instance.skinStyle) {
    AppSkinStyle.kTemplateD => MarketTableHeaderStyleD(title: title, isSelected: isSelected, onTap: onTap),
    _ => MarketTableHeader(title: title, isSelected: isSelected, onTap: onTap),
  };
}

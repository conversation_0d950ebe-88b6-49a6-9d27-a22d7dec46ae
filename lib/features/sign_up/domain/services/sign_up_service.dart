import 'package:gp_stock_app/core/models/apis/sign_up.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import '../models/sign_up_request.dart';

/// Provides static methods for sign-up operations
/// 提供静态方法用于注册
class SignUpService {
  /// Static register method
  /// 静态注册方法
  static Future<ResponseModel<bool>> register({required SignUpRequest signUpRequest}) async {
    return await SignUpApi.register(signUpRequest: signUpRequest);
  }
}

import 'dart:async';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:rxdart/rxdart.dart';

import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import 'package:gp_stock_app/core/models/apis/auth.dart';
import '../models/login/login_response.dart';

class AuthService {
  static final BehaviorSubject<LoginResponse?> _loginController = BehaviorSubject<LoginResponse?>();

  /// Static login stream for direct access
  /// 静态登录流，用于直接访问
  static Stream<LoginResponse?> get loginStream => _loginController.stream;

  /// Static method to add login response
  /// 静态方法，用于添加登录响应
  static void addLoginResponse(LoginResponse? loginResponse) {
    _loginController.add(loginResponse);
  }

  /// Static login method
  /// 静态登录方法，用于登录
  static Future<ResponseModel<LoginResponse?>> login({
    required String mobile,
    required String password,
    required String smsCode,
    required LoginMode mode,
    String? validate,
  }) async {
    final userModel = await AuthApi.login(
      mobile: mobile,
      password: password,
      smsCode: smsCode,
      mode: mode,
      validate: validate,
    );

    // Convert UserModel to LoginResponse
    if (userModel != null) {
      // Convert UserModel to UserData
      final userData = UserData()
        ..auth = userModel.auth
        ..authStatus = userModel.authStatus
        ..avatar = userModel.avatar
        ..countryCode = userModel.countryCode
        ..email = userModel.email
        ..fromType = userModel.fromType
        ..id = userModel.id
        ..idCard = userModel.idCard
        ..inviteCode = userModel.inviteCode
        ..isPayment = userModel.isPayment
        ..level = userModel.level
        ..mobile = userModel.mobile
        ..nickname = userModel.nickname
        ..pid = userModel.pid
        ..profiles = userModel.profiles
        ..realName = userModel.realName
        ..score = userModel.score
        ..sex = userModel.sex
        ..status = userModel.status
        ..tradeStatus = userModel.tradeStatus
        ..type = userModel.type;

      final loginResponse = LoginResponse()
        ..code = 0
        ..data = userData
        ..msg = 'success';

      return ResponseModel(data: loginResponse, code: 0, msg: 'success');
    } else {
      return ResponseModel(data: null, code: -1, msg: 'Login failed');
    }
  }

  /// Static logout method
  /// 静态登出方法，用于登出
  static Future<ResponseModel<bool>> logout() async {
    final result = await AuthApi.logout();
    return ResponseModel(data: result, code: 0, msg: 'success');
  }

  /// Static request OTP method
  /// 静态请求OTP方法，用于请求OTP
  static Future<ResponseModel<bool>> requestOTP({
    required String phoneNumber,
    required String sendType,
  }) async {
    final result = await AuthApi.requestOTP(
      phoneNumber: phoneNumber,
      sendType: sendType,
    );
    return ResponseModel(data: result, code: 0, msg: 'success');
  }

  /// Static check captcha required method
  /// 静态检查验证码是否需要方法，用于检查验证码是否需要
  static Future<bool> requestWangYiCaptchaRequired() async {
    return await AuthApi.requestWangYiCaptchaRequired();
  }
}

part of 'sign_in_cubit.dart';

class SignInState extends Equatable {
  final int selectedTab;
  final DataStatus loginFetchStatus;
  final bool isRememberPassword;
  final bool isAcceptedTerms;
  final DataStatus otpReqStatus;
  final dynamic globalConfig;
  const SignInState({
    this.selectedTab = 0,
    this.loginFetchStatus = DataStatus.idle,
    this.isRememberPassword = false,
    this.isAcceptedTerms = true,
    this.otpReqStatus = DataStatus.idle,
    this.globalConfig,
  });


  @override
  List<Object?> get props =>
      [selectedTab, loginFetchStatus, isRememberPassword, isAcceptedTerms, otpReqStatus, globalConfig];

  SignInState copyWith({
    int? selectedTab,
    DataStatus? loginFetchStatus,
    bool? isRememberPassword,
    bool? isAcceptedTerms = true,
    bool? clearData = false,
    DataStatus? otpReqStatus,
    dynamic globalConfig,
  }) {
    return SignInState(
      selectedTab: selectedTab ?? this.selectedTab,
      loginFetchStatus: clearData == true ? DataStatus.idle : loginFetchStatus ?? this.loginFetchStatus,
      isRememberPassword: isRememberPassword ?? this.isRememberPassword,
      isAcceptedTerms: isAcceptedTerms ?? this.isAcceptedTerms,
      otpReqStatus: otpReqStatus ?? this.otpReqStatus,
      globalConfig: globalConfig ?? this.globalConfig,
    );
  }
}

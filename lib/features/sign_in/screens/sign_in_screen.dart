import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/logic/app_info/app_info_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/mixins/chat_button_mixin.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';

import '../../../core/api/network/network_helper.dart';
import '../../../core/dependency_injection/injectable.dart';
import '../../../core/utils/validators.dart';
import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/constants/assets.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/routes/routes.dart';
import '../../../shared/widgets/app_header.dart';
import '../../../shared/widgets/buttons/custom_radio_button.dart';
import '../../../shared/widgets/otp_field.dart';
import '../../../shared/widgets/text_fields/text_field_widget.dart';
import '../../account/logic/otp/otp_cubit.dart';
import '../../account/logic/otp/otp_state.dart';
import '../logic/sign_in/sign_in_cubit.dart';
import '../widgets/info_bottom_sheet.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key, this.needRedirection = false});

  final bool needRedirection;

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> with AppHeaderMixin, HideFloatButtonRouteAwareMixin {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            buildAppHeader(context, height: .45.gsh, showHeaderImage: true),
            _LoginForm(needRedirection: widget.needRedirection),
          ],
        ),
      ),
    );
  }
}

class _LoginForm extends StatefulWidget {
  const _LoginForm({this.needRedirection = false});

  final bool needRedirection;

  @override
  State<_LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<_LoginForm> {
  final usernameController = TextEditingController();
  final passwordController = TextEditingController();
  final mobileController = TextEditingController();
  final codeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    Helper.afterInit(() => context.read<SignInCubit>().getRememberPassword().then((value) {
          if (value.$1) {
            usernameController.text = value.$2 ?? '';
            passwordController.text = value.$3 ?? '';
          }
        }));
  }

  @override
  void dispose() {
    usernameController.dispose();
    passwordController.dispose();
    mobileController.dispose();
    codeController.dispose();
    super.dispose();
  }

  void _handleSendVerificationCode(BuildContext context) {
    // Use the form validation to validate the mobile field
    if (!_formKey.currentState!.validate()) {
      return;
    }
    context.read<OtpCubit>().sendOtp(mobileController.text, type: OtpType.login);
  }

  void _handleLogin() {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    final signInCubit = context.read<SignInCubit>();
    if (!signInCubit.state.isAcceptedTerms) {
      NetworkHelper.handleMessage('acceptTerms'.tr(), context);
      return;
    }

    signInCubit.login(
      username: usernameController.text.trim(),
      password: passwordController.text.trim(),
      mobile: mobileController.text.trim(),
      smsCode: codeController.text.trim(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => OtpCubit(),
      child: MultiBlocListener(
        listeners: [
          BlocListener<OtpCubit, OtpState>(
            listenWhen: (previous, current) => previous.sendStatus != current.sendStatus,
            listener: (context, state) {
              if (state.sendStatus == DataStatus.loading) {
                GPEasyLoading.showLoading(message: 'sendingCode'.tr());
              } else if (state.sendStatus == DataStatus.success && state.isSent) {
                GPEasyLoading.showSuccess(message: 'verificationCodeSent'.tr());
              }
            },
          ),
          BlocListener<SignInCubit, SignInState>(
            listenWhen: (previous, current) => previous.loginFetchStatus != current.loginFetchStatus,
            listener: (context, state) {
              if (state.loginFetchStatus == DataStatus.success) {
                GPEasyLoading.showSuccess(message: 'loginSuccess'.tr());
                Navigator.pop(context, widget.needRedirection);
              }
            },
          ),
        ],
        child: Builder(
          builder: (context) {
            return AnimationConfiguration.synchronized(
              duration: const Duration(milliseconds: 800),
              child: SlideAnimation(
                verticalOffset: 100.0,
                child: FadeInAnimation(
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: context.theme.cardColor,
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 30.gw),
                      child: BlocSelector<SignInCubit, SignInState, int>(
                        selector: (state) => state.selectedTab,
                        builder: (context, selectedTab) {
                          return Form(
                            key: _formKey,
                            child: AnimationLimiter(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: AnimationConfiguration.toStaggeredList(
                                  duration: const Duration(milliseconds: 600),
                                  childAnimationBuilder: (widget) => SlideAnimation(
                                    verticalOffset: 50.0,
                                    child: FadeInAnimation(
                                      child: widget,
                                    ),
                                  ),
                                  children: [
                                    24.verticalSpace,
                                    CommonTabBar(
                                      data: ['loginAccount'.tr(), 'loginAccountPhone'.tr()],
                                      onTap: (index) => context.read<SignInCubit>().selectTab(index),
                                      currentIndex: selectedTab,
                                      skin: AppConfig.instance.skinStyle,
                                      backgroundColor: Colors.transparent,
                                    ),
                                    12.verticalSpace,
                                    AnimatedSwitcher(
                                      duration: const Duration(milliseconds: 400),
                                      transitionBuilder: (Widget child, Animation<double> animation) {
                                        return FadeTransition(
                                          opacity: animation,
                                          child: SlideTransition(
                                            position: Tween<Offset>(
                                              begin: const Offset(0.1, 0.0),
                                              end: Offset.zero,
                                            ).animate(animation),
                                            child: child,
                                          ),
                                        );
                                      },
                                      child: selectedTab == 0
                                          ? AccountLogin(
                                              key: const ValueKey('account_login'),
                                              usernameController: usernameController,
                                              passwordController: passwordController,
                                              onLogin: _handleLogin,
                                            )
                                          : MobileLogin(
                                              key: const ValueKey('mobile_login'),
                                              phoneController: mobileController,
                                              verificationCodeController: codeController,
                                              onSendVerificationCode: () => _handleSendVerificationCode(context),
                                              onLogin: _handleLogin,
                                            ),
                                    ),
                                    10.verticalSpace,
                                    _ForgotPassword(
                                      usernameController: usernameController,
                                      passwordController: passwordController,
                                    ),
                                    10.verticalSpace,
                                    BlocBuilder<SignInCubit, SignInState>(
                                      builder: (context, state) {
                                        return AnimationConfiguration.synchronized(
                                          duration: const Duration(milliseconds: 300),
                                          child: ScaleAnimation(
                                              scale: 0.9,
                                              child: CommonButton(
                                                title: 'login'.tr(),
                                                onPressed: _handleLogin,
                                                showLoading: state.loginFetchStatus.isLoading,
                                                style: CommonButtonStyle.primary,
                                              )),
                                        );
                                      },
                                    ),
                                    12.verticalSpace,
                                    _LoginFooter(
                                      usernameController: usernameController,
                                      passwordController: passwordController,
                                    ),
                                    50.verticalSpace,
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class MobileLogin extends StatelessWidget {
  final TextEditingController phoneController;
  final TextEditingController verificationCodeController;
  final VoidCallback onSendVerificationCode;
  final VoidCallback onLogin;

  const MobileLogin({
    super.key,
    required this.phoneController,
    required this.verificationCodeController,
    required this.onSendVerificationCode,
    required this.onLogin,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OtpCubit, OtpState>(
      builder: (context, state) {
        return OtpField(
          mobileController: phoneController,
          codeController: verificationCodeController,
          otpState: state,
          onSendCode: onSendVerificationCode,
        );
      },
    );
  }
}

class AccountLogin extends StatelessWidget {
  final TextEditingController usernameController;
  final TextEditingController passwordController;
  final VoidCallback onLogin;

  const AccountLogin({
    super.key,
    required this.usernameController,
    required this.passwordController,
    required this.onLogin,
  });

  @override
  Widget build(BuildContext context) {
    return AnimationLimiter(
      child: Column(
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 400),
          childAnimationBuilder: (widget) => SlideAnimation(
            verticalOffset: 20.0,
            child: FadeInAnimation(
              child: widget,
            ),
          ),
          children: [
            TextFieldWidget(
              controller: usernameController,
              hintText: 'loginPhonePlaceholder'.tr(),
              textInputType: TextInputType.phone,
              maxLength: 11,
              counterText: '',
              validator: (value) => Validators.validateMobile(value),
              prefixIcon: SvgPicture.asset(
                Assets.mobileIcon,
                fit: BoxFit.scaleDown,
                width: 18.gw,
                height: 18.gh,
              ),
            ),
            21.verticalSpace,
            TextFieldWidget(
              controller: passwordController,
              hintText: 'loginPassword'.tr(),
              textInputType: TextInputType.visiblePassword,
              validator: (value) => Validators.validatePassword(value),
              prefixIcon: SvgPicture.asset(
                Assets.lockIcon,
                fit: BoxFit.scaleDown,
                width: 18.gw,
                height: 18.gh,
              ),
              obscureText: true,
              passwordIcon: true,
            ),
          ],
        ),
      ),
    );
  }
}

class _ForgotPassword extends StatelessWidget {
  final TextEditingController usernameController;
  final TextEditingController passwordController;

  const _ForgotPassword({required this.usernameController, required this.passwordController});

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.synchronized(
      duration: const Duration(milliseconds: 500),
      child: FadeInAnimation(
        child: Row(
          children: [
            BlocSelector<SignInCubit, SignInState, bool>(
              selector: (state) => state.isRememberPassword,
              builder: (context, state) {
                return Container(
                  width: 20.gw,
                  height: 20.gh,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.gr),
                    border: Border.all(color: context.theme.primaryColor),
                  ),
                  child: Checkbox(
                    value: state,
                    onChanged: (value) => context.read<SignInCubit>().rememberPassword(value ?? false,
                        username: usernameController.text.trim(), password: passwordController.text.trim()),
                    checkColor: context.theme.primaryColor,
                    side: BorderSide.none,
                    fillColor: WidgetStateProperty.all(Colors.transparent),
                  ),
                );
              },
            ),
            const SizedBox(width: 8),
            Text(
              'loginRememberPassword'.tr(),
              style: context.textTheme.regular,
            ),
            Spacer(),
            AnimationConfiguration.synchronized(
              duration: const Duration(milliseconds: 300),
              child: ScaleAnimation(
                scale: 0.95,
                child: TextButton(
                  onPressed: () => Navigator.pushNamed(context, routeForgot),
                  child: Text(
                    'loginForgotPassword'.tr(),
                    style: context.textTheme.primary.copyWith(color: context.theme.primaryColor),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _LoginFooter extends StatefulWidget {
  final TextEditingController usernameController;
  final TextEditingController passwordController;

  const _LoginFooter({
    required this.usernameController,
    required this.passwordController,
  });

  @override
  State<_LoginFooter> createState() => _LoginFooterState();
}

class _LoginFooterState extends State<_LoginFooter> {
  @override
  void initState() {
    super.initState();
    Helper.afterInit(() => context.read<SignInCubit>().acceptTerms(false));
  }

  void _toggleTerms(BuildContext context, bool currentValue) {
    context.read<SignInCubit>().acceptTerms(!currentValue);
  }

  void _showTerms(BuildContext context) async {
    final result = getIt<AppInfoCubit>().state.appInfoList.firstWhereOrNull((element) => element.type == 2);
    if (result != null && context.mounted) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        builder: (context) => InfoBottomSheet(
          title: result.title,
          content: result.content,
        ),
      );
    }
  }

  void _showPrivacy(BuildContext context) async {
    final result = getIt<AppInfoCubit>().state.appInfoList.firstWhereOrNull((element) => element.type == 3);
    if (result != null && context.mounted) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        builder: (context) => InfoBottomSheet(
          title: result.title,
          content: result.content,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 600),
            delay: const Duration(milliseconds: 200),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 30.0,
              child: FadeInAnimation(
                child: widget,
              ),
            ),
            children: [
              BlocSelector<SignInCubit, SignInState, bool>(
                selector: (state) => state.isAcceptedTerms,
                builder: (context, isAccepted) {
                  return GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () => _toggleTerms(context, isAccepted),
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.gh),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: 6.gh),
                            child: CustomRadioButton(
                              isSelected: isAccepted,
                              onChange: (value) => _toggleTerms(context, isAccepted),
                            ),
                          ),
                          8.horizontalSpace,
                          Expanded(
                            child: RichText(
                              text: TextSpan(
                                text: '${'loginAgreementText'.tr()} ',
                                style: context.textTheme.regular.fs12.w500.copyWith(
                                  height: 1.4,
                                ),
                                children: [
                                  WidgetSpan(
                                    alignment: PlaceholderAlignment.middle,
                                    child: GestureDetector(
                                      behavior: HitTestBehavior.opaque,
                                      onTap: () => _showTerms(context),
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(vertical: 5.gh),
                                        child: Text(
                                          '${'loginAgreementTerms'.tr()} ',
                                          style: context.textTheme.primary.fs12.w500.copyWith(
                                            color: context.theme.primaryColor,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  TextSpan(
                                    text: '${'loginAgreementAnd'.tr()} ',
                                    style: context.textTheme.regular.fs12.w500,
                                  ),
                                  WidgetSpan(
                                    alignment: PlaceholderAlignment.middle,
                                    child: GestureDetector(
                                      behavior: HitTestBehavior.opaque,
                                      onTap: () => _showPrivacy(context),
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(vertical: 5.gh),
                                        child: Text(
                                          'loginAgreementPrivacy'.tr(),
                                          style: context.textTheme.primary.fs12.w500.copyWith(
                                            color: context.theme.primaryColor,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              18.verticalSpace,
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () async {
                  final result = await Navigator.pushNamed(context, routeSignUp) as Map<String, dynamic>?;

                  if (result != null && context.mounted) {
                    widget.usernameController.text = result['mobile'] ?? '';
                    widget.passwordController.text = result['password'] ?? '';
                  }
                },
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.gh),
                  child: RichText(
                    text: TextSpan(
                      text: '${'loginNoAccount'.tr()} ',
                      style: context.textTheme.regular.w500,
                      children: [
                        TextSpan(
                          text: 'loginRegisterNow'.tr(),
                          style: context.textTheme.primary.w500.copyWith(
                            color: context.theme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:gp_stock_app/core/models/apis/notifications.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import '../models/notification/notification_model.dart';

class NotificationsService {
  /// Static method to get notification list
  static Future<ResponseModel<NotificationModel>> getNotificationList({
    required String messageType,
    required int page,
  }) async {
    return await NotificationsApi.getNotificationList(
      messageType: messageType,
      page: page,
    );
  }

  /// Static method to get notification count
  static Future<ResponseModel<int>> getNotificationCount() async {
    return await NotificationsApi.getNotificationCount();
  }

  /// Static method to mark notification as read
  static Future<ResponseModel<bool>> readNotification({
    required messageId,
  }) async {
    return await NotificationsApi.readNotification(messageId: messageId);
  }
}

import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/models/apis/chat.dart';
import 'package:gp_stock_app/core/models/entities/chat/chat_config_entity.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/tencent_utils.dart';
import 'package:gp_stock_app/features/chat/domain/models/chat_config.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

part 'chat_state.dart';

@singleton
class ChatCubit extends AuthAwareCubit<ChatState> {

  ChatCubit() : super(const ChatState());

  @override
  void onLoggedIn() => initChats();

  @override
  void onLoggedOut() => logout();

  void initChats() async {
    emit(state.copyWith(initialLoading: DataStatus.loading, tencentConnectStatus: DataStatus.loading));
    try {
      final chatConfig = await ChatApi.getUserSig();
      if (chatConfig == null) {
        throw Exception("INCORRECT_VALUES");
      }

      final sdkappid = chatConfig.sdkAppId;
      final userid = chatConfig.userId;
      final usersig = chatConfig.userSig;
      LogD(chatConfig.toString());
      void onLoginSuccess() {
        emit(state.copyWith(isTencentInitialized: true, tencentConnectStatus: DataStatus.success));
      }

      void onConnectFailed(code, error) {
        LogE(error.toString());
        emit(state.copyWith(tencentConnectStatus: DataStatus.failed));
      }

      TencentIMUtils.initTencent(
        sdkappid: int.parse(sdkappid),
        userid: userid,
        usersig: usersig,
        onLoginSuccess: () {
          emit(state.copyWith(initialLoading: DataStatus.success, tencentConnectStatus: DataStatus.success));
        },
        onConnectFailed: (code, error) {
          LogE(error.toString());
          TencentIMUtils.initTencent(
            sdkappid: int.parse(sdkappid),
            userid: userid,
            usersig: usersig,
            onLoginSuccess: onLoginSuccess,
            onConnectFailed: onConnectFailed,
          );
        },
      );
    } catch (e) {
      emit(state.copyWith(initialLoading: DataStatus.failed));
      LogE(e.toString());
    }
  }

  Future<ChatServiceAccountConfig?> getChatServicesAccount() async {
    GPEasyLoading.showLoading();
    try {
      final res = await ChatApi.getChatServiceAccount();
      return res;
    } finally {
      GPEasyLoading.dismiss();
    }
  }

  void logout() async {
    emit(state.copyWith(initialLoading: DataStatus.loading));
    try {
      TencentIMUtils.logoutTencent(
        onLogoutSuccess: () {
          emit(
            state.copyWith(
              initialLoading: DataStatus.success,
              isTencentInitialized: false,
              tencentConnectStatus: DataStatus.idle,
            ),
          );
        },
      );
    } catch (e) {
      emit(state.copyWith(initialLoading: DataStatus.failed));
      LogE(e.toString());
    }
  }
}

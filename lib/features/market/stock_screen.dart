import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';

import '../../shared/constants/enums.dart';
import '../../shared/mixin/animation.dart';
import '../../shared/widgets/pagination/pagination_widget.dart';
import '../home/<USER>/market_data_table.dart';
import 'logic/market/market_cubit.dart';
import 'widgets/general_section.dart';
import 'widgets/market_table_header.dart';
import 'widgets/today_section.dart';
import 'widgets/ui/wr_market.dart';

class StockScreen extends StatefulWidget {
  final bool showBackButton;
  final ScrollController? scrollController;

  const StockScreen({
    super.key,
    this.showBackButton = false,
    this.scrollController,
  });

  @override
  State<StockScreen> createState() => _StockScreenState();
}

class _StockScreenState extends State<StockScreen> with StaggeredAnimation {
  Timer? _marketPollingTimer;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
    _initializeMarketData();
    // _setupMarketPolling();
  }

  void _initializeMarketData() {
    context.read<MarketCubit>().init();
  }

  Future<void> _handleRefresh() async {
    try {
      context.read<MarketCubit>().init(isRefresh: true);
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('refresh_failed'.tr())),
        );
      }
    }
  }

  @override
  void dispose() {
    _marketPollingTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: BlocBuilder<MarketCubit, MarketState>(
          builder: (context, state) {
            return RefreshIndicator(
              key: _refreshIndicatorKey,
              onRefresh: _handleRefresh,
              child: PaginationWidget(
                isPaginating: state.tableFetchStatus == DataStatus.loading && state.isPaginating,
                next: _hasMoreData(state),
                onPagination: (notification) => _handlePagination(state),
                child: _buildScrollableContent(state),
              ),
            );
          },
        ),
      ),
    );
  }

  bool _hasMoreData(MarketState state) {
    return (state.tableData?.data?.list?.length ?? 0) <= (state.tableData?.data?.totalNum ?? 0);
  }

  bool _handlePagination(MarketState state) {
    if (_isMaxDataReached(state)) return false;
    _loadNextPage(state);

    return true;
  }

  bool _isMaxDataReached(MarketState state) {
    return state.tableData?.data?.list?.length == (state.tableData?.data?.totalNum ?? 0);
  }

  bool _isLoadingNextPage = false;

  void _loadNextPage(MarketState state) async {
    if (_isLoadingNextPage) return;

    _isLoadingNextPage = true;
    try {
      final (sortType, order) = context.read<MarketCubit>().getTableSortTypeAndOrder();
      await context.read<MarketCubit>().fetchTableData(
            isLoadMore: true,
            sortType: sortType,
            order: order,
          );
    } finally {
      _isLoadingNextPage = false;
    }
  }

  Widget _buildScrollableContent(MarketState state) {
    return SingleChildScrollView(
      controller: widget.scrollController,
      physics: const AlwaysScrollableScrollPhysics(),
      child: Padding(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 8),
        child: AnimationLimiter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 300),
              childAnimationBuilder: (widget) => SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
                Column(
                  children: [
                    const _TodaySection(),
                    const WRMarket(
                      child: TodaySection(),
                    ),
                    BlocBuilder<MarketCubit, MarketState>(
                      builder: (context, state) => WRMarket(
                        child: MarketGeneralSection(type: 1, tab: state.selectedTodaysTab),
                      ),
                    ),
                    const WRMarket(
                      child: MarketDataTable(),
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _TodaySection extends StatelessWidget {
  const _TodaySection();

  @override
  Widget build(BuildContext context) {
    return BlocSelector<MarketCubit, MarketState, TodaysTab>(
      selector: (state) => state.selectedTodaysTab,
      builder: (context, selectedTab) {
        return AnimationLimiter(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 400),
              childAnimationBuilder: (widget) => SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: _buildMarketTabs(context, selectedTab),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildMarketTabs(BuildContext context, TodaysTab selectedTab) {
    final tabs = [
      (title: 'a_shares'.tr(), tab: TodaysTab.aShares),
      (title: 'hk_shares'.tr(), tab: TodaysTab.hkShares),
      (title: 'us_shares'.tr(), tab: TodaysTab.usShares),
    ];

    return tabs.expand((tabInfo) {
      return [
        if (tabInfo != tabs.first) 15.horizontalSpace,
        MarketTableHeader(
          title: tabInfo.title,
          isSelected: selectedTab == tabInfo.tab,
          onTap: () => context.read<MarketCubit>().updateTodaysTab(tabInfo.tab),
        ),
      ];
    }).toList();
  }
}

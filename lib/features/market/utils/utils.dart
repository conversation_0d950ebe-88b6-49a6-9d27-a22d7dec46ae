import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:decimal/decimal.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';

import '../../../shared/constants/enums.dart';
import '../../../shared/models/stock/stock_response.dart';
import '../domain/models/stock_kline_data.dart';

StockItem? updateStockItem(List parts, List list) {
  final market = parts[0];
  final symbol = parts[3];

  final itemIndex = list.indexWhere((item) => item.market == market && item.symbol == symbol);
  if (itemIndex == -1) return null;

  final latestPrice = double.tryParse(parts[7]);
  final high = double.tryParse(parts[5]);
  final low = double.tryParse(parts[6]);
  final open = double.tryParse(parts[4]);
  final close = double.tryParse(parts[7]);

  final stockList = List<StockItem>.from(list);
  final existingItem = stockList.firstWhereOrNull(
    (item) => item.market == market && item.symbol == symbol,
  );
  if (existingItem == null) return null;

  final updatedItem = existingItem.copyWith(
    latestPrice: latestPrice ?? existingItem.latestPrice,
    high: high ?? existingItem.high,
    low: low ?? existingItem.low,
    open: open ?? existingItem.open,
    close: close ?? existingItem.close,
  );

  final isChanged = updatedItem.latestPrice != existingItem.latestPrice ||
      updatedItem.high != existingItem.high ||
      updatedItem.low != existingItem.low ||
      updatedItem.open != existingItem.open ||
      updatedItem.close != existingItem.close;
  if (!isChanged) return null;

  return updatedItem;
}

int? getStockItemIndex(StockItem item, List parts, List<StockItem> list) {
  final itemIndex = list.indexWhere((item) => item.market == parts[0] && item.symbol == parts[3]);
  if (itemIndex == -1) return null;
  return itemIndex;
}

List<StockItem>? updateStockList(
  int index,
  StockItem item,
  List<StockItem> list,
) {
  final stockList = List<StockItem>.from(list);
  stockList[index] = item;
  return stockList;
}

List? formatMessage(String data) {
  final jsonData = jsonDecode(data);
  if (jsonData['data'] == null) return null;

  List parts = jsonData['data'].split('|');

  if (parts.length < 11) return null;
  return parts;
}

List<String> getSocketSymbols(List<String?>? data) {
  return data?.where((e) => e != null).map((e) => "US|2|Q|${e!}|R").toList() ?? [];
}

List<StockKlineData>? updateKlineList({
  StockKlineResponse? result,
  List<StockKlineData>? coreKlineList,
}) {
  final modifiedList = result?.data?.list?.map((item) {
    return item.copyWith(apiEndTime: item.time);
  }).toList();

  if (modifiedList == null) {
    return null;
  }

  final stockKlineData = result?.data?.copyWith(list: modifiedList);
  if (stockKlineData == null) {
    return null;
  }
  final instrument = stockKlineData.detail?.instrument;
  if (instrument == null) return null;
  final list = List<StockKlineData>.from(coreKlineList ?? []);
  final existingIndex = list.indexWhere((e) {
    final id = e.detail?.instrument;
    if (id == null) return false;
    return instrument == id;
  });
  if (existingIndex != -1) {
    list[existingIndex] = stockKlineData;
  } else {
    list.add(stockKlineData);
  }
  return list;
}

StockKlineData? checkAndGetKlineItem({
  List<StockKlineData>? coreKlineList,
  String? instrument,
}) {
  final existingItem = coreKlineList?.firstWhereOrNull((e) {
    final id = e.detail?.instrument;
    if (id == null) return false;
    return instrument == id;
  });
  return existingItem;
}

class MarketParams {

  final MarketType marketType;
  final TodaysTab todaysTab;
  final int sortType;
  final String orderType;
  final int pageNum;
  final bool isHome;
  final int pageSize;
  const MarketParams({
    required this.marketType,
    required this.todaysTab,
    required this.sortType,
    required this.orderType,
    required this.pageNum,
    required this.pageSize,
    required this.isHome,
  });

  String get queryParams => _buildQueryParams();

  String _buildQueryParams() {
    final baseParams = {
      'marketType': _getMarketTypeParam(),
      'securityType': '1',
      if (!isHome) 'plate': _getPlateName(),
      'field': _getSortTypeParam(),
      'order': orderType,
      'pageNumber': pageNum.toString(),
      'pageSize': pageSize.toString(),
    };

    return baseParams.entries.map((e) => '${e.key}=${e.value}').join('&');
  }

  String _getMarketTypeParam() => switch (todaysTab) {
        TodaysTab.aShares => 'CN',
        TodaysTab.hkShares => 'HK',
        TodaysTab.usShares => 'US',
      };

  String _getSortTypeParam() => switch (sortType) {
        0 => 'volume',
        1 => 'latestPrice',
        2 => 'gain',
        int() => 'volume',
      };

  String _getPlateName() => switch (marketType) {
        // A Shares plates
        MarketType.shenzhen => 'SZSE_PLATE',
        MarketType.shanghai => 'SSE_PLATE',
        MarketType.starMarket => 'STAR_MARKET',
        MarketType.gem => 'SECOND_BOARD',

        // HK plates
        MarketType.gen => 'CREATE_PLATE',
        MarketType.main => 'MAIN_PLATE',

        // US plates
        MarketType.china => 'US_CHINA',
        MarketType.star => 'US_STAR',
      };

  /// Validates if the market type is valid for the given tab
  bool isValidCombination() => todaysTab.marketTypes.contains(marketType);
}

num calcAvailable({
  required num amount,
  required num price,
  required int decimal,
  required num lotSize,
}) {
  if (price == 0) return 0;

  // Convert values to Decimal for precision
  Decimal total = Decimal.parse((amount / price).toStringAsFixed(decimal));

  // Calculate available amount based on lot size
  Decimal available =
      Decimal.parse((total / Decimal.parse(lotSize.toString())).floor().toString()) * Decimal.parse(lotSize.toString());

  return available < Decimal.parse(lotSize.toString()) ? 0 : available.toDouble();
}

({int decimalLength, bool isInteger}) getQuantityStepperProps(StockInfoData? stock) {
  if (stock == null) return (decimalLength: 0, isInteger: true);
  final lotSize = stock.lotSize ?? 0;

  // Check if integer
  final isInteger = (lotSize.truncateToDouble() == lotSize);

  // Decimal length
  int decimalLength = 0;
  if (!isInteger) {
    final lotSizeStr = lotSize.toString();
    if (lotSizeStr.contains('.')) {
      decimalLength = lotSizeStr.split('.')[1].length;
    }
  }
  return (decimalLength: decimalLength, isInteger: isInteger);
}

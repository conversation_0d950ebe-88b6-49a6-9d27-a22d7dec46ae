import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../../shared/widgets/symbol/symbol_chip.dart';
import '../domain/models/stock_reponse/stock_info_response.dart';

class PlateInfoTableRow extends StatelessWidget {
  final StockInfoData data;
  final VoidCallback? onTap;

  const PlateInfoTableRow({
    super.key,
    required this.data,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.gr),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 12.gh, horizontal: 4.gw),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildName(context),
              Expanded(
                flex: 3,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildLatestPrice(context),
                    _buildChangePercentage(context),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildName(BuildContext context) {
    return Expanded(
      flex: 3,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            data.name ?? 'N/A',
            style: context.textTheme.primary.fs12.w500.copyWith(
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 2,
          ),
          4.verticalSpace,
          Row(
            children: [
              SymbolChip(
                name: data.market?.substring(0, 2) ?? '',
                chipColor: context.theme.primaryColor,
              ),
              5.horizontalSpace,
              Text(
                data.symbol ?? 'N/A',
                style: context.textTheme.regular.fs12.w500.ffAkz,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLatestPrice(BuildContext context) {
    final color = (data.gain ?? 0.00).getValueColor(context);
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween<double>(
        begin: 0,
        end: data.latestPrice ?? 0,
      ),
      builder: (context, value, child) {
        return AnimatedDefaultTextStyle(
          duration: const Duration(milliseconds: 200),
          style: context.textTheme.primary.fs13.w500.ffAkz.copyWith(
            color: color,
          ),
          child: data.latestPrice != null
              ? AnimatedFlipCounter(
                  duration: const Duration(milliseconds: 300),
                  fractionDigits: 3,
                  textStyle: context.textTheme.primary.w500.ffAkz.copyWith(
                    color: color,
                  ),
                  value: value,
                )
              : const Text(
                  'N/A',
                  textAlign: TextAlign.center,
                ),
        );
      },
    );
  }

  Widget _buildChangePercentage(BuildContext context) {
    final gain = (data.gain ?? 0.00) * 100;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: 77.gw,
      padding: EdgeInsets.symmetric(
        horizontal: 9.gw,
        vertical: 8.gh,
      ),
      decoration: BoxDecoration(
        color: (data.gain ?? 0.00).getValueColor(context),
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: TweenAnimationBuilder<double>(
        duration: const Duration(milliseconds: 300),
        tween: Tween<double>(
          begin: 0,
          end: gain,
        ),
        builder: (context, value, child) {
          return Text(
            "${value <= 0 ? '' : '+'}${value.toStringAsFixed(2)} %",
            textAlign: TextAlign.center,
            style: context.textTheme.primary.fs13.w700.ffAkz.copyWith(
              color: Colors.white,
            ),
          );
        },
      ),
    );
  }
}

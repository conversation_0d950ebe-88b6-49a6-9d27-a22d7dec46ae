import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/time_range.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class KlineGraph extends StatelessWidget {
  final List<FlSpot> spots;
  final Color textColor;
  final Color lineColor;
  final double width;
  final double height;
  final MainMarketType market;

  const KlineGraph({
    super.key,
    required this.spots,
    required this.textColor,
    required this.lineColor,
    required this.market,
    this.width = 120,
    this.height = 80,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 0.25.gsw,
      height: height.gh,
      child: ShimmerWidget(
        isLoading: spots.isEmpty,
        color: Colors.greenAccent,
        child: LineChart(
          duration: const Duration(milliseconds: 0),
          LineChartData(
            lineTouchData: const LineTouchData(enabled: false),
            gridData: const FlGridData(show: false),
            titlesData: const FlTitlesData(show: false),
            borderData: FlBorderData(
              show: false,
              border: Border.all(
                color: textColor.withAlpha(30),
              ),
            ),
            minX: 0,
            minY: 0,
            maxX: TimeRange.shareMinutesPerDay(market).toDouble(),
            lineBarsData: [
              LineChartBarData(
                spots: spots,
                isCurved: true,
                color: lineColor,
                barWidth: 0.3,
                isStrokeCapRound: false,
                dotData: FlDotData(
                  show: true,
                  getDotPainter: (spot, percent, bar, index) {
                    if (index == spots.length - 1) {
                      return FlDotCirclePainter(
                        radius: 0,
                        color: textColor,
                      );
                    }
                    return FlDotCirclePainter(radius: 0);
                  },
                ),
                belowBarData: BarAreaData(
                  show: true,
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      lineColor.withNewOpacity(0.3),
                      Colors.white.withNewOpacity(0.3),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

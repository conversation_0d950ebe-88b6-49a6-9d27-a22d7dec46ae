import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/market/warning/logic/warning_cubit.dart';
import 'package:gp_stock_app/features/market/warning/logic/warning_state.dart';
import 'package:gp_stock_app/features/market/warning/widgets/warning_tile.dart';
import 'package:gp_stock_app/features/market/warning/widgets/market_tabs_widget.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class WarningListScreen extends StatefulWidget {
  const WarningListScreen({super.key});

  @override
  State<WarningListScreen> createState() => _WarningListScreenState();
}

class _WarningListScreenState extends State<WarningListScreen> {
  final ScrollController _scrollController = ScrollController();
  @override
  void initState() {
    super.initState();
    context.read<WarningCubit>().getWarnList(market: TodaysTab.aShares.market);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent) {
      context.read<WarningCubit>().getWarnList(loadMore: true, market: TodaysTab.aShares.market);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('warningList'.tr()),
      ),
      body: BlocListener<WarningCubit, WarningState>(
        listenWhen: (previous, current) => previous.selectedMarket != current.selectedMarket,
        listener: (context, state) => context.read<WarningCubit>().getWarnList(market: state.selectedMarket.market),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            MarketTabsWidget(
              onTabSelected: (tab) => context.read<WarningCubit>().updateMarket(tab),
            ),
            BlocBuilder<WarningCubit, WarningState>(
              builder: (context, state) {
                if (state.listFetchStatus.isLoading && !state.isLoadMore) {
                  return const Expanded(
                    child: Center(
                      child: SizedBox(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  );
                }
                if (state.listFetchStatus.isFailed || state.warnings.records.isEmpty) {
                  return Expanded(child: TableEmptyWidget());
                }
                return Expanded(
                  child: ListView.separated(
                    itemCount: state.warnings.records.length + 1,
                    controller: _scrollController,
                    separatorBuilder: (context, index) => SizedBox(height: 10.gh),
                    padding: EdgeInsets.symmetric(horizontal: 10.gh, vertical: 10.gh),
                    itemBuilder: (context, index) {
                      if (index >= state.warnings.records.length) {
                        return state.hasMore
                            ? const Center(
                                child: Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: CircularProgressIndicator(),
                                ),
                              )
                            : Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Center(
                                  child: Text(
                                    'noMoreStocksToLoad'.tr(),
                                    style: context.textTheme.regular,
                                  ),
                                ),
                              );
                      }
                      return WarningTile(
                        record: state.warnings.records[index],
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:auto_size_text/auto_size_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/features/market/domain/models/warn_data/warn_data.dart';
import 'package:gp_stock_app/features/market/warning/logic/warning_cubit.dart';
import 'package:gp_stock_app/features/market/warning/logic/warning_state.dart';
import 'package:gp_stock_app/features/market/warning/screens/warning_list_screen.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';

class AddWarningScreen extends StatefulWidget {
  final StockInfoData stockData;
  final bool isUpdate;
  const AddWarningScreen({
    super.key,
    required this.stockData,
    this.isUpdate = false,
  });

  @override
  State<AddWarningScreen> createState() => _AddWarningScreenState();
}

class _AddWarningScreenState extends State<AddWarningScreen> {
  final _priceRoseController = TextEditingController(text: '0');
  final _priceFellController = TextEditingController(text: '0');
  final _dailyIncreaseController = TextEditingController(text: '0');
  final _dailyDeclineController = TextEditingController(text: '0');

  @override
  void initState() {
    super.initState();
    context.read<WarningCubit>().getWarnByInstrument(widget.stockData.instrument);
    if (widget.isUpdate) {
      context.read<WarningCubit>().getStockInfo(widget.stockData.instrument);
    }
  }

  @override
  void dispose() {
    _priceRoseController.dispose();
    _priceFellController.dispose();
    _dailyIncreaseController.dispose();
    _dailyDeclineController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: context.colorTheme.textPrimary,
            size: 20.gw,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          widget.isUpdate ? 'editWarning'.tr() : 'addWarning'.tr(),
          style: context.textTheme.primary.fs18.w600,
        ),
        actions: [
          if (!widget.isUpdate)
            TextButton(
              onPressed: () {
                // Navigator.pushNamed(context, '/mine-warning');
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => BlocProvider.value(
                      value: context.read<WarningCubit>(),
                      child: const WarningListScreen(),
                    ),
                  ),
                );
              },
              child: Text(
                'mineWarning'.tr(),
                style: context.textTheme.primary.copyWith(
                  color: context.theme.primaryColor,
                ),
              ),
            ),
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<WarningCubit, WarningState>(
            listenWhen: (previous, current) => previous.fetchStatus != current.fetchStatus,
            listener: (context, state) {
              if (state.fetchStatus == DataStatus.success) {
                GPEasyLoading.dismiss();
                _priceRoseController.text = state.warnData?.targetUpPrice.toString() ?? '';
                _priceFellController.text = state.warnData?.targetDownPrice.toString() ?? '';
                _dailyIncreaseController.text = state.warnData?.targetUpGain.toString() ?? '';
                _dailyDeclineController.text = state.warnData?.targetDownGain.toString() ?? '';
              }
            },
          ),
          BlocListener<WarningCubit, WarningState>(
            listenWhen: (previous, current) => previous.addStatus != current.addStatus,
            listener: (context, state) {
              if (state.addStatus == DataStatus.success) {
                GPEasyLoading.dismiss();
                GPEasyLoading.showSuccess(message: 'addedSuccessfully'.tr());
              } else if (state.addStatus == DataStatus.failed) {
                GPEasyLoading.dismiss();
                GPEasyLoading.showToast(state.error);
              } else if (state.addStatus == DataStatus.loading) {
                GPEasyLoading.showLoading(message: 'loading'.tr());
              }
            },
          ),
          BlocListener<WarningCubit, WarningState>(
            listenWhen: (previous, current) => previous.updateStatus != current.updateStatus,
            listener: (context, state) {
              if (state.updateStatus == DataStatus.success) {
                GPEasyLoading.dismiss();
                GPEasyLoading.showSuccess(message: 'updatedSuccessfully'.tr());
                Navigator.pop(context, true);
              } else if (state.updateStatus == DataStatus.failed) {
                GPEasyLoading.dismiss();
                GPEasyLoading.showToast(state.error);
              } else if (state.updateStatus == DataStatus.loading) {
                GPEasyLoading.showLoading(message: 'loading'.tr());
              }
            },
          ),
        ],
        child: BlocBuilder<WarningCubit, WarningState>(
          builder: (context, state) {
            return SingleChildScrollView(
              padding: EdgeInsets.all(16.gr),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStockInfo(state.stockInfo),
                  20.verticalSpace,
                  _buildWarningInputs(),
                  40.verticalSpace,
                  CommonButton(
                    title: 'submit'.tr(),
                    onPressed: state.fetchStatus == DataStatus.success ? _submitWarning : null,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStockInfo(StockInfoData? stockInfo) {
    final isEnglish = context.locale.languageCode == 'en';

    return ShadowBox(
      child: Container(
        padding: EdgeInsets.all(4.gr),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              children: [
                AutoSizeText(
                  widget.stockData.name ?? '',
                  style: context.textTheme.primary.w600,
                ),
                8.horizontalSpace,
                SymbolChip(name: widget.stockData.market ?? '', chipColor: context.theme.primaryColor),
                8.horizontalSpace,
                Text(
                  widget.stockData.symbol ?? '',
                  style: context.textTheme.regular,
                ),
              ],
            ),
            12.verticalSpace,
            isEnglish
                ? Column(
                    children: [
                      AmountRow(
                        title: 'currentPrice'.tr(),
                        amount: widget.stockData.latestPrice ?? stockInfo?.latestPrice ?? 0,
                      ),
                      AmountRow(
                        title: 'change'.tr(),
                        amount: widget.stockData.chg ?? stockInfo?.chg ?? 0,
                        color: (widget.stockData.chg ?? stockInfo?.chg ?? 0) > 0
                            ? context.colorTheme.stockGreen
                            : context.colorTheme.stockRed,
                      ),
                      AmountRow(
                        title: 'changePercent'.tr(),
                        suffix: '%',
                        amount: (widget.stockData.gain ?? stockInfo?.gain ?? 0),
                        color: (widget.stockData.gain ?? stockInfo?.gain ?? 0) > 0
                            ? context.colorTheme.stockGreen
                            : context.colorTheme.stockRed,
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    spacing: 4,
                    children: [
                      Expanded(
                        child: AmountRow(
                          isLeft: true,
                          title: 'currentPrice'.tr(),
                          amount: widget.stockData.latestPrice ?? stockInfo?.latestPrice ?? 0,
                        ),
                      ),
                      Expanded(
                        child: AmountRow(
                          isLeft: true,
                          title: 'change'.tr(),
                          amount: widget.stockData.chg ?? stockInfo?.chg ?? 0,
                          color: (widget.stockData.chg ?? stockInfo?.chg ?? 0) > 0
                              ? context.colorTheme.stockGreen
                              : context.colorTheme.stockRed,
                        ),
                      ),
                      Expanded(
                        child: AmountRow(
                          isLeft: true,
                          title: 'changePercent'.tr(),
                          suffix: '%',
                          amount: (widget.stockData.gain ?? stockInfo?.gain ?? 0),
                          color: (widget.stockData.gain ?? stockInfo?.gain ?? 0) > 0
                              ? context.colorTheme.stockGreen
                              : context.colorTheme.stockRed,
                        ),
                      ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildWarningInputs() {
    return ShadowBox(
      child: Theme(
        data: context.theme.copyWith(
          inputDecorationTheme: context.theme.inputDecorationTheme.copyWith(
            border: InputBorder.none,
          ),
        ),
        child: Column(
          children: [
            _buildInputRow('priceRoseTo'.tr(), _priceRoseController),
            Divider(color: context.colorTheme.textRegular.withValues(alpha: 0.3), thickness: 0.5),
            _buildInputRow('priceFellTo'.tr(), _priceFellController),
            Divider(color: context.colorTheme.textRegular.withValues(alpha: 0.3), thickness: 0.5),
            _buildInputRow(
              'dailyIncreaseExceeds'.tr(),
              _dailyIncreaseController,
              suffix: Icon(Icons.percent, color: context.colorTheme.stockGreen),
            ),
            Divider(color: context.colorTheme.textRegular.withValues(alpha: 0.3), thickness: 0.5),
            _buildInputRow(
              'dailyDeclineExceeds'.tr(),
              _dailyDeclineController,
              suffix: Icon(Icons.percent, color: context.colorTheme.stockRed),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputRow(String label, TextEditingController controller, {Widget? suffix}) {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Text(
            label,
            style: context.textTheme.primary.fs15,
          ),
        ),
        Expanded(
          flex: 2,
          child: TextField(
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            controller: controller,
            style: context.textTheme.primary.fs15,
            decoration: InputDecoration(
              suffixIcon: suffix,
              suffixIconConstraints: const BoxConstraints(),
            ),
          ),
        ),
      ],
    );
  }

  void _submitWarning() {
    final id = context.read<WarningCubit>().state.warnData?.id;
    final warn = WarnData(
      market: widget.stockData.market ?? '',
      symbol: widget.stockData.symbol ?? '',
      name: widget.stockData.name ?? '',
      targetUpPrice: double.tryParse(_priceRoseController.text) ?? 0,
      targetDownPrice: double.tryParse(_priceFellController.text) ?? 0,
      targetUpGain: double.tryParse(_dailyIncreaseController.text) ?? 0,
      targetDownGain: double.tryParse(_dailyDeclineController.text) ?? 0,
      id: id ?? 0,
      createTime: context.read<WarningCubit>().state.warnData?.createTime ?? '',
      updateTime: context.read<WarningCubit>().state.warnData?.updateTime ?? '',
      securityType: widget.stockData.securityType ?? '',
      userId: context.read<WarningCubit>().state.warnData?.userId ?? 0,
    );
    if (id != null && id > 0) {
      context.read<WarningCubit>().updateWarn(warn);
    } else {
      context.read<WarningCubit>().addWarn(warn);
    }
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/features/market/domain/models/warn_data/warn_data.dart';
import 'package:gp_stock_app/features/market/warning/logic/warning_cubit.dart';
import 'package:gp_stock_app/features/market/warning/logic/warning_state.dart';
import 'package:gp_stock_app/features/market/warning/screens/add_warning_screen.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';

class WarningTile extends StatefulWidget {
  const WarningTile({super.key, required this.record});
  final WarnData record;

  @override
  State<WarningTile> createState() => _WarningTileState();
}

class _WarningTileState extends State<WarningTile> {
  bool isExpanded = false;
  @override
  Widget build(BuildContext context) {
    return BlocListener<WarningCubit, WarningState>(
      listenWhen: (previous, current) => previous.deleteStatus != current.deleteStatus,
      listener: (context, state) {
        if (state.deleteStatus == DataStatus.loading) {
          GPEasyLoading.showLoading(message: 'loading'.tr());
        } else if (state.deleteStatus == DataStatus.success) {
          GPEasyLoading.showSuccess(message: 'deletedSuccessfully'.tr());
        }
      },
      child: ShadowBox(
        child: Column(
          children: [
            Row(
              spacing: 6.gh,
              children: [
                Text(widget.record.name),
                SymbolChip(
                  name: widget.record.market,
                  chipColor: context.theme.primaryColor,
                ),
                Text(widget.record.symbol),
                Spacer(),
                InkWell(
                  child: Text(
                    'delete'.tr(),
                    style: context.textTheme.stockRed,
                  ),
                  onTap: () {
                    context.read<WarningCubit>().deleteWarn(widget.record.id.toString());
                  },
                ),
                InkWell(
                  child: Text(
                    'edit'.tr(),
                    style: context.textTheme.primary.copyWith(color: context.theme.primaryColor),
                  ),
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => BlocProvider(
                          create: (context) => getIt<WarningCubit>(),
                          child: AddWarningScreen(
                            stockData: StockInfoData(
                              name: widget.record.name,
                              symbol: widget.record.symbol,
                              market: widget.record.market,
                              securityType: widget.record.securityType,
                            ),
                            isUpdate: true,
                          ),
                        ),
                      ),
                    ).then((value) {
                      if (value) {
                        if (context.mounted) {
                          context.read<WarningCubit>().getWarnList(market: widget.record.market);
                        }
                      }
                    });
                  },
                ),
              ],
            ),
            SizedBox(height: 10.gh),
            SizedBox(
              height: 25.gh,
              width: 25.gh,
              child: ExpandIcon(
                padding: EdgeInsets.zero,
                isExpanded: isExpanded,
                onPressed: (value) {
                  setState(() {
                    isExpanded = !isExpanded;
                  });
                },
              ),
            ),
            AnimatedSize(
              duration: Duration(milliseconds: 300),
              curve: Curves.easeInOut,
              child: isExpanded
                  ? Column(
                      children: [
                        SizedBox(height: 10.gh),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          spacing: 4,
                          children: [
                            Expanded(
                              child: AmountRow(
                                title: 'priceRoseTo'.tr(),
                                amount: widget.record.targetUpPrice,
                              ),
                            ),
                            Expanded(
                              child: AmountRow(
                                title: 'priceFellTo'.tr(),
                                amount: widget.record.targetDownPrice,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 10.gh),
                        AmountRow(
                          title: 'dailyIncreaseExceeds'.tr(),
                          amount: widget.record.targetUpGain,
                        ),
                        SizedBox(height: 10.gh),
                        AmountRow(
                          title: 'dailyDeclineExceeds'.tr(),
                          amount: widget.record.targetDownGain,
                        ),
                      ],
                    )
                  : SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'stock_info_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$StockInfoResponseImpl _$$StockInfoResponseImplFromJson(
        Map<String, dynamic> json) =>
    _$StockInfoResponseImpl(
      code: (json['code'] as num?)?.toInt(),
      data: json['data'] == null
          ? null
          : StockInfoData.fromJson(json['data'] as Map<String, dynamic>),
      msg: json['msg'] as String?,
    );

Map<String, dynamic> _$$StockInfoResponseImplToJson(
        _$StockInfoResponseImpl instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

_$StockInfoDataImpl _$$StockInfoDataImplFromJson(Map<String, dynamic> json) =>
    _$StockInfoDataImpl(
      symbol: json['symbol'] as String?,
      industryPlate: json['industryPlate'] as String?,
      marketCategory: json['marketCategory'] as String?,
      precision: (json['precision'] as num?)?.toInt(),
      high52w: (json['high52w'] as num?)?.toDouble(),
      peStatic: (json['peStatic'] as num?)?.toDouble(),
      peLyr: (json['peLyr'] as num?)?.toDouble(),
      securityStatus: (json['securityStatus'] as num?)?.toInt(),
      gain: (json['gain'] as num?)?.toDouble(),
      amplitude: (json['amplitude'] as num?)?.toDouble(),
      high: (json['high'] as num?)?.toDouble(),
      low: (json['low'] as num?)?.toDouble(),
      biddingGain: (json['biddingGain'] as num?)?.toDouble(),
      biddingChg: (json['biddingChg'] as num?)?.toDouble(),
      floatShare: (json['floatShare'] as num?)?.toInt(),
      vwap: (json['vwap'] as num?)?.toInt(),
      dividend: (json['dividend'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      tag: json['tag'] as String?,
      close: (json['close'] as num?)?.toDouble(),
      turnover: (json['turnover'] as num?)?.toDouble(),
      latestPrice: (json['latestPrice'] as num?)?.toDouble(),
      peTtm: (json['peTtm'] as num?)?.toDouble(),
      amount: (json['amount'] as num?)?.toDouble(),
      chg: (json['chg'] as num?)?.toDouble(),
      lotSize: (json['lotSize'] as num?)?.toDouble(),
      biddingLow: (json['biddingLow'] as num?)?.toDouble(),
      marketValue: (json['marketValue'] as num?)?.toDouble(),
      dividendRate: (json['dividendRate'] as num?)?.toDouble(),
      biddingAmount: (json['biddingAmount'] as num?)?.toDouble(),
      biddingPrice: (json['biddingPrice'] as num?)?.toDouble(),
      priceUpLimited: (json['priceUpLimited'] as num?)?.toDouble(),
      priceDownLimited: (json['priceDownLimited'] as num?)?.toDouble(),
      totalShares: (json['totalShares'] as num?)?.toInt(),
      biddingHigh: (json['biddingHigh'] as num?)?.toDouble(),
      market: json['market'] as String?,
      volume: (json['volume'] as num?)?.toInt(),
      pb: (json['pb'] as num?)?.toDouble(),
      securityType: json['securityType'] as String?,
      low52w: (json['low52w'] as num?)?.toDouble(),
      name: json['name'] as String?,
      biddingVolume: (json['biddingVolume'] as num?)?.toInt(),
      biddingTime: (json['biddingTime'] as num?)?.toInt(),
      latestTime: (json['latestTime'] as num?)?.toInt(),
      open: (json['open'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$$StockInfoDataImplToJson(_$StockInfoDataImpl instance) =>
    <String, dynamic>{
      'symbol': instance.symbol,
      'industryPlate': instance.industryPlate,
      'marketCategory': instance.marketCategory,
      'precision': instance.precision,
      'high52w': instance.high52w,
      'peStatic': instance.peStatic,
      'peLyr': instance.peLyr,
      'securityStatus': instance.securityStatus,
      'gain': instance.gain,
      'amplitude': instance.amplitude,
      'high': instance.high,
      'low': instance.low,
      'biddingGain': instance.biddingGain,
      'biddingChg': instance.biddingChg,
      'floatShare': instance.floatShare,
      'vwap': instance.vwap,
      'dividend': instance.dividend,
      'currency': instance.currency,
      'tag': instance.tag,
      'close': instance.close,
      'turnover': instance.turnover,
      'latestPrice': instance.latestPrice,
      'peTtm': instance.peTtm,
      'amount': instance.amount,
      'chg': instance.chg,
      'lotSize': instance.lotSize,
      'biddingLow': instance.biddingLow,
      'marketValue': instance.marketValue,
      'dividendRate': instance.dividendRate,
      'biddingAmount': instance.biddingAmount,
      'biddingPrice': instance.biddingPrice,
      'priceUpLimited': instance.priceUpLimited,
      'priceDownLimited': instance.priceDownLimited,
      'totalShares': instance.totalShares,
      'biddingHigh': instance.biddingHigh,
      'market': instance.market,
      'volume': instance.volume,
      'pb': instance.pb,
      'securityType': instance.securityType,
      'low52w': instance.low52w,
      'name': instance.name,
      'biddingVolume': instance.biddingVolume,
      'biddingTime': instance.biddingTime,
      'latestTime': instance.latestTime,
      'open': instance.open,
    };

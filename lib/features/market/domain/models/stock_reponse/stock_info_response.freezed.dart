// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'stock_info_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

StockInfoResponse _$StockInfoResponseFromJson(Map<String, dynamic> json) {
  return _StockInfoResponse.fromJson(json);
}

/// @nodoc
mixin _$StockInfoResponse {
  int? get code => throw _privateConstructorUsedError;
  StockInfoData? get data => throw _privateConstructorUsedError;
  String? get msg => throw _privateConstructorUsedError;

  /// Serializes this StockInfoResponse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StockInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StockInfoResponseCopyWith<StockInfoResponse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StockInfoResponseCopyWith<$Res> {
  factory $StockInfoResponseCopyWith(
          StockInfoResponse value, $Res Function(StockInfoResponse) then) =
      _$StockInfoResponseCopyWithImpl<$Res, StockInfoResponse>;
  @useResult
  $Res call({int? code, StockInfoData? data, String? msg});

  $StockInfoDataCopyWith<$Res>? get data;
}

/// @nodoc
class _$StockInfoResponseCopyWithImpl<$Res, $Val extends StockInfoResponse>
    implements $StockInfoResponseCopyWith<$Res> {
  _$StockInfoResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StockInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_value.copyWith(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as StockInfoData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of StockInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $StockInfoDataCopyWith<$Res>? get data {
    if (_value.data == null) {
      return null;
    }

    return $StockInfoDataCopyWith<$Res>(_value.data!, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$StockInfoResponseImplCopyWith<$Res>
    implements $StockInfoResponseCopyWith<$Res> {
  factory _$$StockInfoResponseImplCopyWith(_$StockInfoResponseImpl value,
          $Res Function(_$StockInfoResponseImpl) then) =
      __$$StockInfoResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? code, StockInfoData? data, String? msg});

  @override
  $StockInfoDataCopyWith<$Res>? get data;
}

/// @nodoc
class __$$StockInfoResponseImplCopyWithImpl<$Res>
    extends _$StockInfoResponseCopyWithImpl<$Res, _$StockInfoResponseImpl>
    implements _$$StockInfoResponseImplCopyWith<$Res> {
  __$$StockInfoResponseImplCopyWithImpl(_$StockInfoResponseImpl _value,
      $Res Function(_$StockInfoResponseImpl) _then)
      : super(_value, _then);

  /// Create a copy of StockInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = freezed,
    Object? data = freezed,
    Object? msg = freezed,
  }) {
    return _then(_$StockInfoResponseImpl(
      code: freezed == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int?,
      data: freezed == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as StockInfoData?,
      msg: freezed == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StockInfoResponseImpl implements _StockInfoResponse {
  const _$StockInfoResponseImpl({this.code, this.data, this.msg});

  factory _$StockInfoResponseImpl.fromJson(Map<String, dynamic> json) =>
      _$$StockInfoResponseImplFromJson(json);

  @override
  final int? code;
  @override
  final StockInfoData? data;
  @override
  final String? msg;

  @override
  String toString() {
    return 'StockInfoResponse(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StockInfoResponseImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, data, msg);

  /// Create a copy of StockInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StockInfoResponseImplCopyWith<_$StockInfoResponseImpl> get copyWith =>
      __$$StockInfoResponseImplCopyWithImpl<_$StockInfoResponseImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StockInfoResponseImplToJson(
      this,
    );
  }
}

abstract class _StockInfoResponse implements StockInfoResponse {
  const factory _StockInfoResponse(
      {final int? code,
      final StockInfoData? data,
      final String? msg}) = _$StockInfoResponseImpl;

  factory _StockInfoResponse.fromJson(Map<String, dynamic> json) =
      _$StockInfoResponseImpl.fromJson;

  @override
  int? get code;
  @override
  StockInfoData? get data;
  @override
  String? get msg;

  /// Create a copy of StockInfoResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StockInfoResponseImplCopyWith<_$StockInfoResponseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

StockInfoData _$StockInfoDataFromJson(Map<String, dynamic> json) {
  return _StockInfoData.fromJson(json);
}

/// @nodoc
mixin _$StockInfoData {
  String? get symbol => throw _privateConstructorUsedError;
  String? get industryPlate => throw _privateConstructorUsedError;
  String? get marketCategory => throw _privateConstructorUsedError;
  int? get precision => throw _privateConstructorUsedError;
  double? get high52w => throw _privateConstructorUsedError;
  double? get peStatic => throw _privateConstructorUsedError;
  double? get peLyr => throw _privateConstructorUsedError;
  int? get securityStatus => throw _privateConstructorUsedError;
  double? get gain => throw _privateConstructorUsedError;
  double? get amplitude => throw _privateConstructorUsedError;
  double? get high => throw _privateConstructorUsedError;
  double? get low => throw _privateConstructorUsedError;
  double? get biddingGain => throw _privateConstructorUsedError;
  double? get biddingChg => throw _privateConstructorUsedError;
  int? get floatShare => throw _privateConstructorUsedError;
  int? get vwap => throw _privateConstructorUsedError;
  double? get dividend => throw _privateConstructorUsedError;
  String? get currency => throw _privateConstructorUsedError;
  String? get tag => throw _privateConstructorUsedError;
  double? get close => throw _privateConstructorUsedError;
  double? get turnover => throw _privateConstructorUsedError;
  double? get latestPrice => throw _privateConstructorUsedError;
  double? get peTtm => throw _privateConstructorUsedError;
  double? get amount => throw _privateConstructorUsedError;
  double? get chg => throw _privateConstructorUsedError;
  double? get lotSize => throw _privateConstructorUsedError;
  double? get biddingLow => throw _privateConstructorUsedError;
  double? get marketValue => throw _privateConstructorUsedError;
  double? get dividendRate => throw _privateConstructorUsedError;
  double? get biddingAmount => throw _privateConstructorUsedError;
  double? get biddingPrice => throw _privateConstructorUsedError;
  double? get priceUpLimited => throw _privateConstructorUsedError;
  double? get priceDownLimited => throw _privateConstructorUsedError;
  int? get totalShares => throw _privateConstructorUsedError;
  double? get biddingHigh => throw _privateConstructorUsedError;
  String? get market => throw _privateConstructorUsedError;
  int? get volume => throw _privateConstructorUsedError;
  double? get pb => throw _privateConstructorUsedError;
  String? get securityType => throw _privateConstructorUsedError;
  double? get low52w => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  int? get biddingVolume => throw _privateConstructorUsedError;
  int? get biddingTime => throw _privateConstructorUsedError;
  int? get latestTime => throw _privateConstructorUsedError;
  double? get open => throw _privateConstructorUsedError;

  /// Serializes this StockInfoData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of StockInfoData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $StockInfoDataCopyWith<StockInfoData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $StockInfoDataCopyWith<$Res> {
  factory $StockInfoDataCopyWith(
          StockInfoData value, $Res Function(StockInfoData) then) =
      _$StockInfoDataCopyWithImpl<$Res, StockInfoData>;
  @useResult
  $Res call(
      {String? symbol,
      String? industryPlate,
      String? marketCategory,
      int? precision,
      double? high52w,
      double? peStatic,
      double? peLyr,
      int? securityStatus,
      double? gain,
      double? amplitude,
      double? high,
      double? low,
      double? biddingGain,
      double? biddingChg,
      int? floatShare,
      int? vwap,
      double? dividend,
      String? currency,
      String? tag,
      double? close,
      double? turnover,
      double? latestPrice,
      double? peTtm,
      double? amount,
      double? chg,
      double? lotSize,
      double? biddingLow,
      double? marketValue,
      double? dividendRate,
      double? biddingAmount,
      double? biddingPrice,
      double? priceUpLimited,
      double? priceDownLimited,
      int? totalShares,
      double? biddingHigh,
      String? market,
      int? volume,
      double? pb,
      String? securityType,
      double? low52w,
      String? name,
      int? biddingVolume,
      int? biddingTime,
      int? latestTime,
      double? open});
}

/// @nodoc
class _$StockInfoDataCopyWithImpl<$Res, $Val extends StockInfoData>
    implements $StockInfoDataCopyWith<$Res> {
  _$StockInfoDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of StockInfoData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? symbol = freezed,
    Object? industryPlate = freezed,
    Object? marketCategory = freezed,
    Object? precision = freezed,
    Object? high52w = freezed,
    Object? peStatic = freezed,
    Object? peLyr = freezed,
    Object? securityStatus = freezed,
    Object? gain = freezed,
    Object? amplitude = freezed,
    Object? high = freezed,
    Object? low = freezed,
    Object? biddingGain = freezed,
    Object? biddingChg = freezed,
    Object? floatShare = freezed,
    Object? vwap = freezed,
    Object? dividend = freezed,
    Object? currency = freezed,
    Object? tag = freezed,
    Object? close = freezed,
    Object? turnover = freezed,
    Object? latestPrice = freezed,
    Object? peTtm = freezed,
    Object? amount = freezed,
    Object? chg = freezed,
    Object? lotSize = freezed,
    Object? biddingLow = freezed,
    Object? marketValue = freezed,
    Object? dividendRate = freezed,
    Object? biddingAmount = freezed,
    Object? biddingPrice = freezed,
    Object? priceUpLimited = freezed,
    Object? priceDownLimited = freezed,
    Object? totalShares = freezed,
    Object? biddingHigh = freezed,
    Object? market = freezed,
    Object? volume = freezed,
    Object? pb = freezed,
    Object? securityType = freezed,
    Object? low52w = freezed,
    Object? name = freezed,
    Object? biddingVolume = freezed,
    Object? biddingTime = freezed,
    Object? latestTime = freezed,
    Object? open = freezed,
  }) {
    return _then(_value.copyWith(
      symbol: freezed == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String?,
      industryPlate: freezed == industryPlate
          ? _value.industryPlate
          : industryPlate // ignore: cast_nullable_to_non_nullable
              as String?,
      marketCategory: freezed == marketCategory
          ? _value.marketCategory
          : marketCategory // ignore: cast_nullable_to_non_nullable
              as String?,
      precision: freezed == precision
          ? _value.precision
          : precision // ignore: cast_nullable_to_non_nullable
              as int?,
      high52w: freezed == high52w
          ? _value.high52w
          : high52w // ignore: cast_nullable_to_non_nullable
              as double?,
      peStatic: freezed == peStatic
          ? _value.peStatic
          : peStatic // ignore: cast_nullable_to_non_nullable
              as double?,
      peLyr: freezed == peLyr
          ? _value.peLyr
          : peLyr // ignore: cast_nullable_to_non_nullable
              as double?,
      securityStatus: freezed == securityStatus
          ? _value.securityStatus
          : securityStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      gain: freezed == gain
          ? _value.gain
          : gain // ignore: cast_nullable_to_non_nullable
              as double?,
      amplitude: freezed == amplitude
          ? _value.amplitude
          : amplitude // ignore: cast_nullable_to_non_nullable
              as double?,
      high: freezed == high
          ? _value.high
          : high // ignore: cast_nullable_to_non_nullable
              as double?,
      low: freezed == low
          ? _value.low
          : low // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingGain: freezed == biddingGain
          ? _value.biddingGain
          : biddingGain // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingChg: freezed == biddingChg
          ? _value.biddingChg
          : biddingChg // ignore: cast_nullable_to_non_nullable
              as double?,
      floatShare: freezed == floatShare
          ? _value.floatShare
          : floatShare // ignore: cast_nullable_to_non_nullable
              as int?,
      vwap: freezed == vwap
          ? _value.vwap
          : vwap // ignore: cast_nullable_to_non_nullable
              as int?,
      dividend: freezed == dividend
          ? _value.dividend
          : dividend // ignore: cast_nullable_to_non_nullable
              as double?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      close: freezed == close
          ? _value.close
          : close // ignore: cast_nullable_to_non_nullable
              as double?,
      turnover: freezed == turnover
          ? _value.turnover
          : turnover // ignore: cast_nullable_to_non_nullable
              as double?,
      latestPrice: freezed == latestPrice
          ? _value.latestPrice
          : latestPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      peTtm: freezed == peTtm
          ? _value.peTtm
          : peTtm // ignore: cast_nullable_to_non_nullable
              as double?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      chg: freezed == chg
          ? _value.chg
          : chg // ignore: cast_nullable_to_non_nullable
              as double?,
      lotSize: freezed == lotSize
          ? _value.lotSize
          : lotSize // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingLow: freezed == biddingLow
          ? _value.biddingLow
          : biddingLow // ignore: cast_nullable_to_non_nullable
              as double?,
      marketValue: freezed == marketValue
          ? _value.marketValue
          : marketValue // ignore: cast_nullable_to_non_nullable
              as double?,
      dividendRate: freezed == dividendRate
          ? _value.dividendRate
          : dividendRate // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingAmount: freezed == biddingAmount
          ? _value.biddingAmount
          : biddingAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingPrice: freezed == biddingPrice
          ? _value.biddingPrice
          : biddingPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      priceUpLimited: freezed == priceUpLimited
          ? _value.priceUpLimited
          : priceUpLimited // ignore: cast_nullable_to_non_nullable
              as double?,
      priceDownLimited: freezed == priceDownLimited
          ? _value.priceDownLimited
          : priceDownLimited // ignore: cast_nullable_to_non_nullable
              as double?,
      totalShares: freezed == totalShares
          ? _value.totalShares
          : totalShares // ignore: cast_nullable_to_non_nullable
              as int?,
      biddingHigh: freezed == biddingHigh
          ? _value.biddingHigh
          : biddingHigh // ignore: cast_nullable_to_non_nullable
              as double?,
      market: freezed == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String?,
      volume: freezed == volume
          ? _value.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as int?,
      pb: freezed == pb
          ? _value.pb
          : pb // ignore: cast_nullable_to_non_nullable
              as double?,
      securityType: freezed == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String?,
      low52w: freezed == low52w
          ? _value.low52w
          : low52w // ignore: cast_nullable_to_non_nullable
              as double?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      biddingVolume: freezed == biddingVolume
          ? _value.biddingVolume
          : biddingVolume // ignore: cast_nullable_to_non_nullable
              as int?,
      biddingTime: freezed == biddingTime
          ? _value.biddingTime
          : biddingTime // ignore: cast_nullable_to_non_nullable
              as int?,
      latestTime: freezed == latestTime
          ? _value.latestTime
          : latestTime // ignore: cast_nullable_to_non_nullable
              as int?,
      open: freezed == open
          ? _value.open
          : open // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$StockInfoDataImplCopyWith<$Res>
    implements $StockInfoDataCopyWith<$Res> {
  factory _$$StockInfoDataImplCopyWith(
          _$StockInfoDataImpl value, $Res Function(_$StockInfoDataImpl) then) =
      __$$StockInfoDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? symbol,
      String? industryPlate,
      String? marketCategory,
      int? precision,
      double? high52w,
      double? peStatic,
      double? peLyr,
      int? securityStatus,
      double? gain,
      double? amplitude,
      double? high,
      double? low,
      double? biddingGain,
      double? biddingChg,
      int? floatShare,
      int? vwap,
      double? dividend,
      String? currency,
      String? tag,
      double? close,
      double? turnover,
      double? latestPrice,
      double? peTtm,
      double? amount,
      double? chg,
      double? lotSize,
      double? biddingLow,
      double? marketValue,
      double? dividendRate,
      double? biddingAmount,
      double? biddingPrice,
      double? priceUpLimited,
      double? priceDownLimited,
      int? totalShares,
      double? biddingHigh,
      String? market,
      int? volume,
      double? pb,
      String? securityType,
      double? low52w,
      String? name,
      int? biddingVolume,
      int? biddingTime,
      int? latestTime,
      double? open});
}

/// @nodoc
class __$$StockInfoDataImplCopyWithImpl<$Res>
    extends _$StockInfoDataCopyWithImpl<$Res, _$StockInfoDataImpl>
    implements _$$StockInfoDataImplCopyWith<$Res> {
  __$$StockInfoDataImplCopyWithImpl(
      _$StockInfoDataImpl _value, $Res Function(_$StockInfoDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of StockInfoData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? symbol = freezed,
    Object? industryPlate = freezed,
    Object? marketCategory = freezed,
    Object? precision = freezed,
    Object? high52w = freezed,
    Object? peStatic = freezed,
    Object? peLyr = freezed,
    Object? securityStatus = freezed,
    Object? gain = freezed,
    Object? amplitude = freezed,
    Object? high = freezed,
    Object? low = freezed,
    Object? biddingGain = freezed,
    Object? biddingChg = freezed,
    Object? floatShare = freezed,
    Object? vwap = freezed,
    Object? dividend = freezed,
    Object? currency = freezed,
    Object? tag = freezed,
    Object? close = freezed,
    Object? turnover = freezed,
    Object? latestPrice = freezed,
    Object? peTtm = freezed,
    Object? amount = freezed,
    Object? chg = freezed,
    Object? lotSize = freezed,
    Object? biddingLow = freezed,
    Object? marketValue = freezed,
    Object? dividendRate = freezed,
    Object? biddingAmount = freezed,
    Object? biddingPrice = freezed,
    Object? priceUpLimited = freezed,
    Object? priceDownLimited = freezed,
    Object? totalShares = freezed,
    Object? biddingHigh = freezed,
    Object? market = freezed,
    Object? volume = freezed,
    Object? pb = freezed,
    Object? securityType = freezed,
    Object? low52w = freezed,
    Object? name = freezed,
    Object? biddingVolume = freezed,
    Object? biddingTime = freezed,
    Object? latestTime = freezed,
    Object? open = freezed,
  }) {
    return _then(_$StockInfoDataImpl(
      symbol: freezed == symbol
          ? _value.symbol
          : symbol // ignore: cast_nullable_to_non_nullable
              as String?,
      industryPlate: freezed == industryPlate
          ? _value.industryPlate
          : industryPlate // ignore: cast_nullable_to_non_nullable
              as String?,
      marketCategory: freezed == marketCategory
          ? _value.marketCategory
          : marketCategory // ignore: cast_nullable_to_non_nullable
              as String?,
      precision: freezed == precision
          ? _value.precision
          : precision // ignore: cast_nullable_to_non_nullable
              as int?,
      high52w: freezed == high52w
          ? _value.high52w
          : high52w // ignore: cast_nullable_to_non_nullable
              as double?,
      peStatic: freezed == peStatic
          ? _value.peStatic
          : peStatic // ignore: cast_nullable_to_non_nullable
              as double?,
      peLyr: freezed == peLyr
          ? _value.peLyr
          : peLyr // ignore: cast_nullable_to_non_nullable
              as double?,
      securityStatus: freezed == securityStatus
          ? _value.securityStatus
          : securityStatus // ignore: cast_nullable_to_non_nullable
              as int?,
      gain: freezed == gain
          ? _value.gain
          : gain // ignore: cast_nullable_to_non_nullable
              as double?,
      amplitude: freezed == amplitude
          ? _value.amplitude
          : amplitude // ignore: cast_nullable_to_non_nullable
              as double?,
      high: freezed == high
          ? _value.high
          : high // ignore: cast_nullable_to_non_nullable
              as double?,
      low: freezed == low
          ? _value.low
          : low // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingGain: freezed == biddingGain
          ? _value.biddingGain
          : biddingGain // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingChg: freezed == biddingChg
          ? _value.biddingChg
          : biddingChg // ignore: cast_nullable_to_non_nullable
              as double?,
      floatShare: freezed == floatShare
          ? _value.floatShare
          : floatShare // ignore: cast_nullable_to_non_nullable
              as int?,
      vwap: freezed == vwap
          ? _value.vwap
          : vwap // ignore: cast_nullable_to_non_nullable
              as int?,
      dividend: freezed == dividend
          ? _value.dividend
          : dividend // ignore: cast_nullable_to_non_nullable
              as double?,
      currency: freezed == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as String?,
      tag: freezed == tag
          ? _value.tag
          : tag // ignore: cast_nullable_to_non_nullable
              as String?,
      close: freezed == close
          ? _value.close
          : close // ignore: cast_nullable_to_non_nullable
              as double?,
      turnover: freezed == turnover
          ? _value.turnover
          : turnover // ignore: cast_nullable_to_non_nullable
              as double?,
      latestPrice: freezed == latestPrice
          ? _value.latestPrice
          : latestPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      peTtm: freezed == peTtm
          ? _value.peTtm
          : peTtm // ignore: cast_nullable_to_non_nullable
              as double?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      chg: freezed == chg
          ? _value.chg
          : chg // ignore: cast_nullable_to_non_nullable
              as double?,
      lotSize: freezed == lotSize
          ? _value.lotSize
          : lotSize // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingLow: freezed == biddingLow
          ? _value.biddingLow
          : biddingLow // ignore: cast_nullable_to_non_nullable
              as double?,
      marketValue: freezed == marketValue
          ? _value.marketValue
          : marketValue // ignore: cast_nullable_to_non_nullable
              as double?,
      dividendRate: freezed == dividendRate
          ? _value.dividendRate
          : dividendRate // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingAmount: freezed == biddingAmount
          ? _value.biddingAmount
          : biddingAmount // ignore: cast_nullable_to_non_nullable
              as double?,
      biddingPrice: freezed == biddingPrice
          ? _value.biddingPrice
          : biddingPrice // ignore: cast_nullable_to_non_nullable
              as double?,
      priceUpLimited: freezed == priceUpLimited
          ? _value.priceUpLimited
          : priceUpLimited // ignore: cast_nullable_to_non_nullable
              as double?,
      priceDownLimited: freezed == priceDownLimited
          ? _value.priceDownLimited
          : priceDownLimited // ignore: cast_nullable_to_non_nullable
              as double?,
      totalShares: freezed == totalShares
          ? _value.totalShares
          : totalShares // ignore: cast_nullable_to_non_nullable
              as int?,
      biddingHigh: freezed == biddingHigh
          ? _value.biddingHigh
          : biddingHigh // ignore: cast_nullable_to_non_nullable
              as double?,
      market: freezed == market
          ? _value.market
          : market // ignore: cast_nullable_to_non_nullable
              as String?,
      volume: freezed == volume
          ? _value.volume
          : volume // ignore: cast_nullable_to_non_nullable
              as int?,
      pb: freezed == pb
          ? _value.pb
          : pb // ignore: cast_nullable_to_non_nullable
              as double?,
      securityType: freezed == securityType
          ? _value.securityType
          : securityType // ignore: cast_nullable_to_non_nullable
              as String?,
      low52w: freezed == low52w
          ? _value.low52w
          : low52w // ignore: cast_nullable_to_non_nullable
              as double?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      biddingVolume: freezed == biddingVolume
          ? _value.biddingVolume
          : biddingVolume // ignore: cast_nullable_to_non_nullable
              as int?,
      biddingTime: freezed == biddingTime
          ? _value.biddingTime
          : biddingTime // ignore: cast_nullable_to_non_nullable
              as int?,
      latestTime: freezed == latestTime
          ? _value.latestTime
          : latestTime // ignore: cast_nullable_to_non_nullable
              as int?,
      open: freezed == open
          ? _value.open
          : open // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$StockInfoDataImpl extends _StockInfoData {
  const _$StockInfoDataImpl(
      {this.symbol,
      this.industryPlate,
      this.marketCategory,
      this.precision,
      this.high52w,
      this.peStatic,
      this.peLyr,
      this.securityStatus,
      this.gain,
      this.amplitude,
      this.high,
      this.low,
      this.biddingGain,
      this.biddingChg,
      this.floatShare,
      this.vwap,
      this.dividend,
      this.currency,
      this.tag,
      this.close,
      this.turnover,
      this.latestPrice,
      this.peTtm,
      this.amount,
      this.chg,
      this.lotSize,
      this.biddingLow,
      this.marketValue,
      this.dividendRate,
      this.biddingAmount,
      this.biddingPrice,
      this.priceUpLimited,
      this.priceDownLimited,
      this.totalShares,
      this.biddingHigh,
      this.market,
      this.volume,
      this.pb,
      this.securityType,
      this.low52w,
      this.name,
      this.biddingVolume,
      this.biddingTime,
      this.latestTime,
      this.open})
      : super._();

  factory _$StockInfoDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$StockInfoDataImplFromJson(json);

  @override
  final String? symbol;
  @override
  final String? industryPlate;
  @override
  final String? marketCategory;
  @override
  final int? precision;
  @override
  final double? high52w;
  @override
  final double? peStatic;
  @override
  final double? peLyr;
  @override
  final int? securityStatus;
  @override
  final double? gain;
  @override
  final double? amplitude;
  @override
  final double? high;
  @override
  final double? low;
  @override
  final double? biddingGain;
  @override
  final double? biddingChg;
  @override
  final int? floatShare;
  @override
  final int? vwap;
  @override
  final double? dividend;
  @override
  final String? currency;
  @override
  final String? tag;
  @override
  final double? close;
  @override
  final double? turnover;
  @override
  final double? latestPrice;
  @override
  final double? peTtm;
  @override
  final double? amount;
  @override
  final double? chg;
  @override
  final double? lotSize;
  @override
  final double? biddingLow;
  @override
  final double? marketValue;
  @override
  final double? dividendRate;
  @override
  final double? biddingAmount;
  @override
  final double? biddingPrice;
  @override
  final double? priceUpLimited;
  @override
  final double? priceDownLimited;
  @override
  final int? totalShares;
  @override
  final double? biddingHigh;
  @override
  final String? market;
  @override
  final int? volume;
  @override
  final double? pb;
  @override
  final String? securityType;
  @override
  final double? low52w;
  @override
  final String? name;
  @override
  final int? biddingVolume;
  @override
  final int? biddingTime;
  @override
  final int? latestTime;
  @override
  final double? open;

  @override
  String toString() {
    return 'StockInfoData(symbol: $symbol, industryPlate: $industryPlate, marketCategory: $marketCategory, precision: $precision, high52w: $high52w, peStatic: $peStatic, peLyr: $peLyr, securityStatus: $securityStatus, gain: $gain, amplitude: $amplitude, high: $high, low: $low, biddingGain: $biddingGain, biddingChg: $biddingChg, floatShare: $floatShare, vwap: $vwap, dividend: $dividend, currency: $currency, tag: $tag, close: $close, turnover: $turnover, latestPrice: $latestPrice, peTtm: $peTtm, amount: $amount, chg: $chg, lotSize: $lotSize, biddingLow: $biddingLow, marketValue: $marketValue, dividendRate: $dividendRate, biddingAmount: $biddingAmount, biddingPrice: $biddingPrice, priceUpLimited: $priceUpLimited, priceDownLimited: $priceDownLimited, totalShares: $totalShares, biddingHigh: $biddingHigh, market: $market, volume: $volume, pb: $pb, securityType: $securityType, low52w: $low52w, name: $name, biddingVolume: $biddingVolume, biddingTime: $biddingTime, latestTime: $latestTime, open: $open)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$StockInfoDataImpl &&
            (identical(other.symbol, symbol) || other.symbol == symbol) &&
            (identical(other.industryPlate, industryPlate) ||
                other.industryPlate == industryPlate) &&
            (identical(other.marketCategory, marketCategory) ||
                other.marketCategory == marketCategory) &&
            (identical(other.precision, precision) ||
                other.precision == precision) &&
            (identical(other.high52w, high52w) || other.high52w == high52w) &&
            (identical(other.peStatic, peStatic) ||
                other.peStatic == peStatic) &&
            (identical(other.peLyr, peLyr) || other.peLyr == peLyr) &&
            (identical(other.securityStatus, securityStatus) ||
                other.securityStatus == securityStatus) &&
            (identical(other.gain, gain) || other.gain == gain) &&
            (identical(other.amplitude, amplitude) ||
                other.amplitude == amplitude) &&
            (identical(other.high, high) || other.high == high) &&
            (identical(other.low, low) || other.low == low) &&
            (identical(other.biddingGain, biddingGain) ||
                other.biddingGain == biddingGain) &&
            (identical(other.biddingChg, biddingChg) ||
                other.biddingChg == biddingChg) &&
            (identical(other.floatShare, floatShare) ||
                other.floatShare == floatShare) &&
            (identical(other.vwap, vwap) || other.vwap == vwap) &&
            (identical(other.dividend, dividend) ||
                other.dividend == dividend) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.tag, tag) || other.tag == tag) &&
            (identical(other.close, close) || other.close == close) &&
            (identical(other.turnover, turnover) ||
                other.turnover == turnover) &&
            (identical(other.latestPrice, latestPrice) ||
                other.latestPrice == latestPrice) &&
            (identical(other.peTtm, peTtm) || other.peTtm == peTtm) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.chg, chg) || other.chg == chg) &&
            (identical(other.lotSize, lotSize) || other.lotSize == lotSize) &&
            (identical(other.biddingLow, biddingLow) ||
                other.biddingLow == biddingLow) &&
            (identical(other.marketValue, marketValue) ||
                other.marketValue == marketValue) &&
            (identical(other.dividendRate, dividendRate) ||
                other.dividendRate == dividendRate) &&
            (identical(other.biddingAmount, biddingAmount) ||
                other.biddingAmount == biddingAmount) &&
            (identical(other.biddingPrice, biddingPrice) ||
                other.biddingPrice == biddingPrice) &&
            (identical(other.priceUpLimited, priceUpLimited) ||
                other.priceUpLimited == priceUpLimited) &&
            (identical(other.priceDownLimited, priceDownLimited) ||
                other.priceDownLimited == priceDownLimited) &&
            (identical(other.totalShares, totalShares) ||
                other.totalShares == totalShares) &&
            (identical(other.biddingHigh, biddingHigh) ||
                other.biddingHigh == biddingHigh) &&
            (identical(other.market, market) || other.market == market) &&
            (identical(other.volume, volume) || other.volume == volume) &&
            (identical(other.pb, pb) || other.pb == pb) &&
            (identical(other.securityType, securityType) ||
                other.securityType == securityType) &&
            (identical(other.low52w, low52w) || other.low52w == low52w) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.biddingVolume, biddingVolume) ||
                other.biddingVolume == biddingVolume) &&
            (identical(other.biddingTime, biddingTime) ||
                other.biddingTime == biddingTime) &&
            (identical(other.latestTime, latestTime) ||
                other.latestTime == latestTime) &&
            (identical(other.open, open) || other.open == open));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        symbol,
        industryPlate,
        marketCategory,
        precision,
        high52w,
        peStatic,
        peLyr,
        securityStatus,
        gain,
        amplitude,
        high,
        low,
        biddingGain,
        biddingChg,
        floatShare,
        vwap,
        dividend,
        currency,
        tag,
        close,
        turnover,
        latestPrice,
        peTtm,
        amount,
        chg,
        lotSize,
        biddingLow,
        marketValue,
        dividendRate,
        biddingAmount,
        biddingPrice,
        priceUpLimited,
        priceDownLimited,
        totalShares,
        biddingHigh,
        market,
        volume,
        pb,
        securityType,
        low52w,
        name,
        biddingVolume,
        biddingTime,
        latestTime,
        open
      ]);

  /// Create a copy of StockInfoData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$StockInfoDataImplCopyWith<_$StockInfoDataImpl> get copyWith =>
      __$$StockInfoDataImplCopyWithImpl<_$StockInfoDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$StockInfoDataImplToJson(
      this,
    );
  }
}

abstract class _StockInfoData extends StockInfoData {
  const factory _StockInfoData(
      {final String? symbol,
      final String? industryPlate,
      final String? marketCategory,
      final int? precision,
      final double? high52w,
      final double? peStatic,
      final double? peLyr,
      final int? securityStatus,
      final double? gain,
      final double? amplitude,
      final double? high,
      final double? low,
      final double? biddingGain,
      final double? biddingChg,
      final int? floatShare,
      final int? vwap,
      final double? dividend,
      final String? currency,
      final String? tag,
      final double? close,
      final double? turnover,
      final double? latestPrice,
      final double? peTtm,
      final double? amount,
      final double? chg,
      final double? lotSize,
      final double? biddingLow,
      final double? marketValue,
      final double? dividendRate,
      final double? biddingAmount,
      final double? biddingPrice,
      final double? priceUpLimited,
      final double? priceDownLimited,
      final int? totalShares,
      final double? biddingHigh,
      final String? market,
      final int? volume,
      final double? pb,
      final String? securityType,
      final double? low52w,
      final String? name,
      final int? biddingVolume,
      final int? biddingTime,
      final int? latestTime,
      final double? open}) = _$StockInfoDataImpl;
  const _StockInfoData._() : super._();

  factory _StockInfoData.fromJson(Map<String, dynamic> json) =
      _$StockInfoDataImpl.fromJson;

  @override
  String? get symbol;
  @override
  String? get industryPlate;
  @override
  String? get marketCategory;
  @override
  int? get precision;
  @override
  double? get high52w;
  @override
  double? get peStatic;
  @override
  double? get peLyr;
  @override
  int? get securityStatus;
  @override
  double? get gain;
  @override
  double? get amplitude;
  @override
  double? get high;
  @override
  double? get low;
  @override
  double? get biddingGain;
  @override
  double? get biddingChg;
  @override
  int? get floatShare;
  @override
  int? get vwap;
  @override
  double? get dividend;
  @override
  String? get currency;
  @override
  String? get tag;
  @override
  double? get close;
  @override
  double? get turnover;
  @override
  double? get latestPrice;
  @override
  double? get peTtm;
  @override
  double? get amount;
  @override
  double? get chg;
  @override
  double? get lotSize;
  @override
  double? get biddingLow;
  @override
  double? get marketValue;
  @override
  double? get dividendRate;
  @override
  double? get biddingAmount;
  @override
  double? get biddingPrice;
  @override
  double? get priceUpLimited;
  @override
  double? get priceDownLimited;
  @override
  int? get totalShares;
  @override
  double? get biddingHigh;
  @override
  String? get market;
  @override
  int? get volume;
  @override
  double? get pb;
  @override
  String? get securityType;
  @override
  double? get low52w;
  @override
  String? get name;
  @override
  int? get biddingVolume;
  @override
  int? get biddingTime;
  @override
  int? get latestTime;
  @override
  double? get open;

  /// Create a copy of StockInfoData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$StockInfoDataImplCopyWith<_$StockInfoDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

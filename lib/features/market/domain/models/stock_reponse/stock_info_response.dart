import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';

part 'stock_info_response.freezed.dart';
part 'stock_info_response.g.dart';

StockInfoResponse stockInfoResponseFromJson(String str) => StockInfoResponse.fromJson(json.decode(str));

String stockInfoResponseToJson(StockInfoResponse data) => json.encode(data.toJson());

@freezed
class StockInfoResponse with _$StockInfoResponse {
  const factory StockInfoResponse({
    int? code,
    StockInfoData? data,
    String? msg,
  }) = _StockInfoResponse;

  factory StockInfoResponse.fromJson(Map<String, dynamic> json) => _$StockInfoResponseFromJson(json);
}

@freezed
class StockInfoData with _$StockInfoData {
  const StockInfoData._();
  const factory StockInfoData({
    String? symbol,
    String? industryPlate,
    String? marketCategory,
    int? precision,
    double? high52w,
    double? peStatic,
    double? peLyr,
    int? securityStatus,
    double? gain,
    double? amplitude,
    double? high,
    double? low,
    double? biddingGain,
    double? biddingChg,
    int? floatShare,
    int? vwap,
    double? dividend,
    String? currency,
    String? tag,
    double? close,
    double? turnover,
    double? latestPrice,
    double? peTtm,
    double? amount,
    double? chg,
    double? lotSize,
    double? biddingLow,
    double? marketValue,
    double? dividendRate,
    double? biddingAmount,
    double? biddingPrice,
    double? priceUpLimited,
    double? priceDownLimited,
    int? totalShares,
    double? biddingHigh,
    String? market,
    int? volume,
    double? pb,
    String? securityType,
    double? low52w,
    String? name,
    int? biddingVolume,
    int? biddingTime,
    int? latestTime,
    double? open,
  }) = _StockInfoData;

  factory StockInfoData.fromJson(Map<String, dynamic> json) => _$StockInfoDataFromJson(json);

  String get instrument => '$market|$securityType|$symbol';
  Instrument get instrumentInfo => Instrument(instrument: instrument);
}

import 'package:gp_stock_app/features/account/domain/models/quotes/company_news/company_news_response.dart';
import 'package:gp_stock_app/features/market/domain/models/broker_queue/broker_queue.dart';
import 'package:gp_stock_app/features/market/domain/models/create_order_parameter/create_order_parameter.dart';
import 'package:gp_stock_app/features/market/domain/models/index_page/index_page_response_order.dart';
import 'package:gp_stock_app/features/market/domain/models/index_page/index_page_response_position.dart';
import 'package:gp_stock_app/features/market/domain/models/index_stock/index_stock.dart';
import 'package:gp_stock_app/features/market/domain/models/market_status/market_status.dart';
import 'package:gp_stock_app/features/market/domain/models/tick_list_response.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';

import '../../../../core/api/network/models/result.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/models/market_depth/market_depth.dart';
import '../../../../shared/models/stock/stock_response.dart';
import '../models/component_stock_response.dart';
import '../models/depth_quote_model.dart';
import '../models/dist_response.dart';
import '../models/plate_info_request/plate_info_request.dart';
import '../models/plate_info_response.dart/plate_info_response.dart';
import '../models/plate_response.dart';
import '../models/search/market_search.dart';
import '../models/stock_kline_data.dart';
import '../models/stock_reponse/stock_info_response.dart';
import '../models/stock_table_response.dart';

abstract class MarketRepository {
  const MarketRepository();

  Future<ResponseResult<IndexPagePositionResponse>> getIndexPagePosition({
    required int pageNumber,
    required int pageSize,
  });

  Future<ResponseResult<IndexPageOrderResponse>> getIndexPageOrder({
    required int pageNumber,
    required int pageSize,
  });

  Future<ResponseResult<List<MarketStatus>>> getMarketStatus();

  Future<ResponseResult<PlateResponse>> fetchPlateList(
      {TodaysTab? tab, int? pageNum, int? securityType, String? field, String? order});

  Future<ResponseResult<StockResponse>> fetchStockList();

  Future<ResponseResult<StockTableResponse>> fetchTableData({
    required int sortType,
    required String order,
    required int pageNum,
    required MarketType marketTableType,
    required TodaysTab todaysTab,
    required bool isHome,
    required int pageSize,
  });

  Future<ResponseResult<DistResponse>> fetchDist({TodaysTab? type});

  Future<ResponseResult<StockKlineResponse>> fetchKlineData(StockItem stock, String period);

  Future<ResponseResult<StockKlineResponse>> fetchKlineData2({required String instrument, required String period});

  Future<ResponseResult<ComponentStockResponse>> fetchComponentStock();

  Future<ResponseResult<MarketSearchResponse>> fetchSearchResults(String query, {String? market});

  Future<ResponseResult<DepthQuoteModel>> fetchDepthQuote(StockItem stock);

  Future<ResponseResult<StockInfoResponse>> getStockInfo(String instrument);

  Future<ResponseResult<MarketDepth>> getMarketDepth(String instrument);

  Future<ResponseResult<bool>> createOrder(CreateOrderParameter parameter);

  Future<ResponseResult<PlateInfoResponse>> getPlateInfo(PlateInfoRequest request);

  Future<ResponseResult<bool>> cancelOrder({required int orderId});

  Future<ResponseResult<bool>> setExpireTime({required int positionId, required int timerValue});

  Future<ResponseResult<List<IndexStock>>> getIndexList();

  Future<ResponseResult<BrokerQueueResponse>> getBrokerQueue(String instrument);

  Future<ResponseResult<TickListResponse>> getTickList({
    required String instrument,
    required int page,
    required int limitPerPage,
  });

  Future<ResponseResult<CompanyNewsResponse>> getCompanyNews({
    required Instrument instrument,
    required int page,
    required int limitPerPage,
  });
}

import 'package:dio/dio.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/company_news/company_news_response.dart';
import 'package:gp_stock_app/features/market/domain/models/broker_queue/broker_queue.dart';
import 'package:gp_stock_app/features/market/domain/models/create_order_parameter/create_order_parameter.dart';
import 'package:gp_stock_app/features/market/domain/models/index_page/index_page_response_order.dart';
import 'package:gp_stock_app/features/market/domain/models/index_page/index_page_response_position.dart';
import 'package:gp_stock_app/features/market/domain/models/index_stock/index_stock.dart';
import 'package:gp_stock_app/features/market/domain/models/market_status/market_status.dart';
import 'package:gp_stock_app/features/market/domain/models/search/market_search.dart';
import 'package:gp_stock_app/features/market/domain/models/tick_list_response.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/models/market_depth/market_depth.dart';
import '../../../../shared/models/stock/stock_response.dart';
import '../../utils/utils.dart';
import '../models/component_stock_response.dart';
import '../models/depth_quote_model.dart';
import '../models/dist_response.dart';
import '../models/plate_info_request/plate_info_request.dart';
import '../models/plate_info_response.dart/plate_info_response.dart';
import '../models/plate_response.dart';
import '../models/stock_kline_data.dart';
import '../models/stock_reponse/stock_info_response.dart';
import '../models/stock_table_response.dart';
import '../repositories/market_repository.dart';

@Injectable(as: MarketRepository)
class MarketService implements MarketRepository {
  @override
  Future<ResponseResult<IndexPagePositionResponse>> getIndexPagePosition(
      {required int pageNumber, required int pageSize}) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.indexPagePosition,
        queryParameters: {
          'pageNumber': pageNumber,
          'pageSize': pageSize,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final data = response.data['data'];
          return ResponseResult(data: IndexPagePositionResponse.fromJson(data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch index page');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<IndexPageOrderResponse>> getIndexPageOrder(
      {required int pageNumber, required int pageSize}) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.indexPageOrder,
        queryParameters: {
          'pageNumber': pageNumber,
          'pageSize': pageSize,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final data = response.data['data'];
          return ResponseResult(data: IndexPageOrderResponse.fromJson(data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch index page');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<List<MarketStatus>>> getMarketStatus() async {
    try {
      final Response response =
          await NetworkProvider().get(ApiEndpoints.getMarketStatus, queryParameters: {'marketTypes': 'US|HK|CN'});

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          final data = response.data['data']['list'];
          return ResponseResult(data: List<MarketStatus>.from(data.map((e) => MarketStatus.fromJson(e))));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch market status');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<StockResponse>> fetchStockList() async {
    return ResponseResult(data: StockResponse());
    // try {
    //   final Response response = await NetworkProvider(baseUrl: Urls.quoteBaseUrl).get(
    //     '${ApiEndpoints.getStockList}?instruments=US|2|.DJI,US|2|.IXIC,US|2|.INX&format=0',
    //     options: Options(
    //       headers: {'auth': true},
    //     ),
    //     force: false,
    //   );

    //   if (response.statusCode == 200 || response.statusCode == 201) {
    //     if (response.data['code'] == 1) {
    //       return ResponseResult(data: StockResponse.fromJson(response.data));
    //     } else {
    //       return ResponseResult(error: response.data['msg']);
    //     }
    //   } else {
    //     return ResponseResult(error: 'Failed to fetch stock list');
    //   }
    // } on DioException catch (e) {
    //   return ResponseResult(error: e.error.toString());
    // }
  }

  @override
  Future<ResponseResult<DistResponse>> fetchDist({TodaysTab? type}) async {
    final params = switch (type) {
      TodaysTab.aShares => 'markets=SZSE,SSE&securityType=1',
      TodaysTab.hkShares => 'markets=HKEX&securityType=1',
      TodaysTab.usShares => 'markets=US&securityType=1',
      _ => '',
    };
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getGainDistribution}?$params',
        // options: Options(
        //   headers: {'auth': true},
        // ),
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: DistResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch distribution');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<PlateResponse>> fetchPlateList(
      {TodaysTab? tab, int? pageNum, int? securityType, String? field, String? order}) async {
    final params = switch (tab) {
      TodaysTab.aShares => 'markets=SZSE,SSE&securityType=1',
      TodaysTab.hkShares => 'markets=HKEX&securityType=1',
      TodaysTab.usShares => 'markets=US&securityType=1',
      _ => '',
    };
    try {
      final Response response = await NetworkProvider().get('${ApiEndpoints.getPlateList}?$params', queryParameters: {
        'pageNumber': pageNum,
        'pageSize': 20,
        'securityType': securityType,
        'field': field,
        'order': order,
      });

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: PlateResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch plate list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<StockTableResponse>> fetchTableData({
    required int sortType,
    required String order,
    required int pageNum,
    required MarketType marketTableType,
    required TodaysTab todaysTab,
    required bool isHome,
    required int pageSize,
  }) async {
    final params = MarketParams(
        marketType: marketTableType,
        todaysTab: todaysTab,
        sortType: sortType,
        orderType: order,
        pageNum: pageNum,
        isHome: isHome,
        pageSize: pageSize);

    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getMarketPlate}?${params.queryParams}',
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: StockTableResponse.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch table data');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<StockKlineResponse>> fetchKlineData(
    StockItem stock,
    String period,
  ) async {
    return ResponseResult(data: StockKlineResponse());
    // try {
    //   final instrument = getInstrumentId(stock);
    //   final newPeriod = period == "realtime" ? "month" : period;
    //   final Response response = await NetworkProvider(baseUrl: Urls.quoteBaseUrl).get(
    //     '${ApiEndpoints.getTimeline}?period=$newPeriod&instrument=$instrument&right=0',
    //     options: Options(
    //       headers: {'auth': true},
    //     ),
    //     force: false,
    //   );

    //   if (response.statusCode == 200 || response.statusCode == 201) {
    //     final result = StockKlineResponse.fromJson(response.data);
    //     return ResponseResult(
    //       data: result,
    //     );
    //   } else {
    //     return ResponseResult(error: 'Failed to fetch kline data');
    //   }
    // } on DioException catch (e) {
    //   return ResponseResult(error: e.error.toString());
    // }
  }

  @override
  Future<ResponseResult<ComponentStockResponse>> fetchComponentStock() async {
    return ResponseResult(data: ComponentStockResponse());
    // try {
    //   final Response response = await NetworkProvider().get(
    //     ApiEndpoints.getComponentStock,
    //     options: Options(
    //       headers: {'auth': true},
    //     ),
    //     force: false,
    //   );

    //   if (response.statusCode == 200 || response.statusCode == 201) {
    //     if (response.data['code'] == 200) {
    //       return ResponseResult(
    //         data: ComponentStockResponse.fromJson(response.data),
    //       );
    //     } else {
    //       return ResponseResult(error: response.data['msg']);
    //     }
    //   } else {
    //     return ResponseResult(error: 'Failed to fetch component stock');
    //   }
    // } on DioException catch (e) {
    //   return ResponseResult(error: e.error.toString());
    // }
  }

  @override
  Future<ResponseResult<MarketSearchResponse>> fetchSearchResults(String query, {String? market}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.search}?keyword=$query${market != null ? '&market=$market' : ''}',
        isAuthRequired: true,
        force: false,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: MarketSearchResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch search results');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<DepthQuoteModel>> fetchDepthQuote(
    StockItem stock,
  ) async {
    return ResponseResult(data: DepthQuoteModel());
    //   try {
    //     final instrument = getInstrumentId(stock);
    //     final Response response = await NetworkProvider(baseUrl: Urls.quoteBaseUrl).get(
    //       '${ApiEndpoints.depthQuote}?instrument=$instrument&depth=10',
    //       options: Options(
    //         headers: {'auth': true},
    //       ),
    //       force: false,
    //     );

    //     if (response.statusCode == 200 || response.statusCode == 201) {
    //       if (response.data['code'] == 1) {
    //         return ResponseResult(
    //           data: DepthQuoteModel.fromJson(response.data),
    //         );
    //       } else {
    //         return ResponseResult(error: response.data['msg']);
    //       }
    //     } else {
    //       return ResponseResult(error: 'Failed to depth quote');
    //     }
    //   } on DioException catch (e) {
    //     return ResponseResult(error: e.error.toString());
    //   }
  }

  @override
  Future<ResponseResult<StockInfoResponse>> getStockInfo(String instrument) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getStockInfo,
        queryParameters: {'instrument': instrument},
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: StockInfoResponse.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch stock info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<MarketDepth>> getMarketDepth(String instrument) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getMarketDepth,
        isAuthRequired: true,
        queryParameters: {'instrument': instrument, 'depth': 10}, //todo: change depth
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: MarketDepth.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch stock info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> createOrder(CreateOrderParameter parameter) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.createOrder,
        isAuthRequired: true,
        data: parameter.toJson(),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to create order');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<PlateInfoResponse>> getPlateInfo(PlateInfoRequest request) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getPlateInfo,
        isAuthRequired: true,
        queryParameters: request.toJson()..addAll({'pageSize': 20}),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: PlateInfoResponse.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch plate info');
      }
    } on Error catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> cancelOrder({required int orderId}) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.cancelOrder,
        isAuthRequired: true,
        data: {'id': orderId},
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to cancel order', data: false);
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString(), data: false);
    }
  }

  @override
  Future<ResponseResult<StockKlineResponse>> fetchKlineData2(
      {required String instrument, required String period}) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getKlineData}?period=$period&instrument=$instrument',
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: StockKlineResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch kline data');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<bool>> setExpireTime({required int positionId, required int timerValue}) async {
    try {
      final Response response = await NetworkProvider().post(
        ApiEndpoints.setExpireTime,
        isAuthRequired: true,
        data: {'positionId': positionId, 'timerValue': timerValue},
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: true);
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to set expire time');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<List<IndexStock>>> getIndexList() async {
    try {
      final response = await NetworkProvider().get(ApiEndpoints.getIndexList);
      if (response.statusCode == 200) {
        final indexList = List<IndexStock>.from(response.data['data'].map((e) => IndexStock.fromJson(e)));
        return ResponseResult(data: indexList);
      } else {
        return ResponseResult(error: response.data['msg']);
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<BrokerQueueResponse>> getBrokerQueue(String instrument) async {
    try {
      final response = await NetworkProvider().get(
        ApiEndpoints.brokerQueue,
        queryParameters: {'instrument': instrument},
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: BrokerQueueResponse.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch broker queue');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<TickListResponse>> getTickList({
    required String instrument,
    required int page,
    required int limitPerPage,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getTickList,
        queryParameters: {
          'instrument': instrument,
          'pageNumber': page,
          'pageSize': limitPerPage,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: TickListResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch tick list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<CompanyNewsResponse>> getCompanyNews({
    required Instrument instrument,
    required int page,
    required int limitPerPage,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.companyNews,
        queryParameters: {
          'symbol': instrument.symbol,
          'market': instrument.market,
          'securityType': instrument.securityType,
          'pageNumber': page,
          'pageSize': limitPerPage,
        },
        isAuthRequired: true,
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: CompanyNewsResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to fetch company news');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }
}

import 'dart:async';

import 'package:collection/collection.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/market_status/market_status.dart';
import '../../domain/repositories/market_repository.dart';

part 'market_status_state.dart';

@singleton
class MarketStatusCubit extends Cubit<MarketStatusState> {
  MarketStatusCubit(this.marketRepo) : super(const MarketStatusState());

  final MarketRepository marketRepo;
  Timer? _updateTimer;
  static const Duration _updateInterval = Duration(seconds: 5);

  @override
  Future<void> close() {
    _stopPeriodicUpdates();
    return super.close();
  }

  void init() async {
    // Stop any existing timer before starting a new one
    _stopPeriodicUpdates();

    // Fetch market status immediately
    await _fetchMarketStatus();

    // Start periodic updates
    _startPeriodicUpdates();
  }

  void _startPeriodicUpdates() {
    _updateTimer = Timer.periodic(_updateInterval, (_) {
      _fetchMarketStatus();
    });
  }

  void _stopPeriodicUpdates() {
    _updateTimer?.cancel();
    _updateTimer = null;
  }

  Future<void> _fetchMarketStatus() async {
    emit(state.copyWith(marketStatusFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.getMarketStatus();

      if (result.data != null) {
        emit(
          state.copyWith(
            marketStatusFetchStatus: DataStatus.success,
            marketStatus: result.data,
          ),
        );
      } else {
        emit(
          state.copyWith(
            marketStatusFetchStatus: DataStatus.failed,
            error: result.error,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          marketStatusFetchStatus: DataStatus.failed,
          error: e.toString(),
        ),
      );
      
    }
  }

  MarketStatus? fetchMarketStatus(String marketType) {
    return state.marketStatus.firstWhereOrNull((element) => element.marketType == marketType);
  }

  String _market(String marketType) => switch (marketType) {
        'SZSE' => 'CN',
        'SSE' => 'CN',
        'HKEX' => 'HK',
        'US' => 'US',
        _ => marketType,
      };
  bool isMarketOpen(String? marketType) {
    if (marketType == null) return false;
    final marketStatus = fetchMarketStatus(_market(marketType));
    return marketStatus?.statusInfo.status == 2;
  }
}

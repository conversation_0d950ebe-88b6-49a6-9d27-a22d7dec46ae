import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/market/domain/models/index_stock/index_stock.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/features/market/domain/repositories/market_repository.dart';
import 'package:gp_stock_app/features/market/domain/service/market_service.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/constants/web_socket_actions.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/mixin/web_socket_mixin.dart';
import 'package:injectable/injectable.dart';

part 'index_trade_cubit.freezed.dart';
part 'index_trade_state.dart';

class IndexStockInfo extends Equatable {
  final StockInfoResponse stockInfo;
  final StockKlineResponse klineData;

  const IndexStockInfo({required this.stockInfo, required this.klineData});

  IndexStockInfo copyWith({
    StockInfoResponse? stockInfo,
    StockKlineResponse? klineData,
  }) {
    return IndexStockInfo(
      stockInfo: stockInfo ?? this.stockInfo,
      klineData: klineData ?? this.klineData,
    );
  }

  @override
  List<Object?> get props => [stockInfo, klineData];
}

@singleton
class IndexTradeCubit extends Cubit<IndexTradeState> with WebSocketMixin {
  final MarketRepository marketRepo = MarketService();

  IndexTradeCubit() : super(const IndexTradeState());

  void updateSelectedIndex(
    int index, {
    bool animate = false,
  }) {
    emit(state.copyWith(selectedIndex: index, animate: animate));
  }

  void updateAnimate({
    bool? animate,
  }) {
    if (animate != null) {
      emit(state.copyWith(animate: animate));
      return;
    }
    if (state.animate) {
      emit(state.copyWith(animate: false));
      emit(state.copyWith(animate: true));
    }
  }

  Future<void> init() async {
    try {
      emit(state.copyWith(status: DataStatus.loading));
      await fetchStockConfigData();
      _listenToWebSocket();
    } catch (e) {
      emit(state.copyWith(status: DataStatus.failed));
    }
  }

  Future<void> _listenToWebSocket() async {
    // Listen for stock index updates
    onMessage(SocketEvents.stockIndexUpdate, loginRequired: true).listen((message) {
      fetchStockConfigData();
    });

    // Listen for market data updates
    onMessage(SocketEvents.market).listen(_handleMarketUpdate);
  }

  void _handleMarketUpdate(dynamic message) {
    // Validate response code
    if (message.data['code'] != 200) return;

    // Parse message data
    final stockMarketUpdate = StockKlineResponse.fromJson(message.data);
    if (stockMarketUpdate.data == null) return;

    // Update stocks with new data
    final updatedStocks = state.indexStocks.map((stock) {
      // Skip update if market is closed
      final isMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(stock.stockInfo.data?.market);
      if (!isMarketOpen) return stock;

      // Skip update if instrument doesn't match
      final isMatchingInstrument = stockMarketUpdate.data?.detail?.instrument == stock.stockInfo.data?.instrument;
      if (!isMatchingInstrument) return stock;

      // Update current stock with new data
      return _updateStockWithKlineData(stock, stockMarketUpdate);
    }).toList();

    emit(state.copyWith(indexStocks: updatedStocks));
  }

  IndexStockInfo _updateStockWithKlineData(IndexStockInfo stock, StockKlineResponse klineData) {
    // merge and filter
    List<KlineItem>? newKlineItems = _mergeAndFilterKlineItem(stock.klineData, klineData);
    // 盘前交易=>1 和 待开盘=>0 清空旧的缓存数据(K线分时图显示空白)
    // Clear cached data (K-line and time-sharing charts will be blank) during pre-market=>1 and pre-opening=>0 phases
    final cnMarketStatus = getIt<MarketStatusCubit>().fetchMarketStatus("CN")?.statusInfo.status;
    final hkMarketStatus = getIt<MarketStatusCubit>().fetchMarketStatus("HK")?.statusInfo.status;
    final usMarketStatus = getIt<MarketStatusCubit>().fetchMarketStatus("US")?.statusInfo.status;
    if (["SZSE", "SSE"].contains(stock.stockInfo.data?.market) && [0, 1].contains(cnMarketStatus)) {
      newKlineItems = [];
    } else if (stock.stockInfo.data?.market == "HKEX" && [0, 1].contains(hkMarketStatus)) {
      newKlineItems = [];
    } else if (stock.stockInfo.data?.market == "US" && [0, 1].contains(usMarketStatus)) {
      newKlineItems = [];
    }

    // 当前价格
    final latestPrice = klineData.data?.detail?.latestPrice ?? 0.0;
    // 昨收价
    final close = klineData.data?.detail?.close ?? 0.0;
    // 涨跌额（Gain = 当前价 − 昨收价）小数点 4舍5入 保留2位
    // 涨跌幅（Change% = 涨跌额 / 昨收价 × 100%）小数点 4舍5入 保留2位
    double gain = double.parse((latestPrice - close).toStringAsFixed(4));
    double chg = 0.0;
    if (gain != 0) {
      chg = gain / close * 100;
    }
    double gainDisplay = double.parse(gain.toStringAsFixed(2));
    double chgDisplay = double.parse(chg.toStringAsFixed(2));

    return stock.copyWith(
      klineData: stock.klineData.copyWith(
        data: stock.klineData.data?.copyWith(
          list: newKlineItems,
        ),
      ),
      stockInfo: stock.stockInfo.copyWith(
        data: stock.stockInfo.data?.copyWith(
          latestPrice: klineData.data?.detail?.latestPrice,
          high: klineData.data?.detail?.high,
          low: klineData.data?.detail?.low,
          close: klineData.data?.detail?.close,
          open: klineData.data?.detail?.open,
          gain: gainDisplay,
          chg: chgDisplay,
        ),
      ),
    );
  }

  /// 合并和过滤数据
  /// merge and filter
  List<KlineItem>? _mergeAndFilterKlineItem(StockKlineResponse? stockKlineResponse, StockKlineResponse klineData) {
    final originalList = stockKlineResponse?.data?.list;
    final newList = klineData.data?.list;

    if (originalList == null || newList == null) {
      return null;
    }

    final mergedList = [...originalList, ...newList];
    return _filterLatestPerMinute(mergedList);
  }

  /// 过滤同一分钟内的多条数据, 只使用最新一条
  /// 数据都按照时间排序
  ///
  /// Filters kline data to keep only the latest entry per minute.
  /// Assumes input data is sorted by timestamp.
  List<KlineItem> _filterLatestPerMinute(List<KlineItem> timestamps) {
    if (timestamps.isEmpty) {
      return [];
    }
    Map<int, KlineItem> groups = {};
    for (var data in timestamps) {
      if (data.time == null) {
        continue;
      }
      int minuteKey = (data.time! / 60).floor(); // 60s = 1 minute
      if (!groups.containsKey(minuteKey) || data.time! >= groups[minuteKey]!.time!) {
        groups[minuteKey] = data;
      }
    }

    List<KlineItem> result = groups.values.toList();
    result.sort((a, b) => a.time!.compareTo(b.time!));

    return result;
  }

  Future<void> fetchStockConfigData() async {
    try {
      emit(state.copyWith(status: DataStatus.loading));
      // First fetch the indexes
      await fetchIndexes();
      fetchStockData();
    } catch (e) {
      emit(state.copyWith(status: DataStatus.failed));
    }
  }

  // Fetch only the index list
  Future<void> fetchIndexes() async {
    try {
      final indexResponse = await marketRepo.getIndexList();
      final indexes = indexResponse.data ?? [];
      emit(state.copyWith(indexes: indexes));
    } catch (e) {
      debugPrint('Error fetching indexes: $e');
    }
  }

  // Then fetch the stock and kline data for each index
  Future<void> fetchStockData() async {
    try {
      final klineFutures = await Future.wait(
        state.indexes.map(
          (e) => marketRepo.fetchKlineData2(instrument: e.instrument, period: 'day').catchError((error) {
            // 返回一个默认的 ResponseResult<StockKlineResponse>
            return ResponseResult(
              data: StockKlineResponse(),
              error: '请求失败',
            );
          }),
        ),
      );

      final indexStocks = <IndexStockInfo>[];
      for (int i = 0; i < state.indexes.length; i++) {
        // 当前价格
        final latestPrice = klineFutures[i].data?.data?.detail?.latestPrice ?? 0.0;
        // 昨收价
        final close = klineFutures[i].data?.data?.detail?.close ?? 0.0;
        // 涨跌额（Gain = 当前价 − 昨收价）小数点 4舍5入 保留2位
        // 涨跌幅（Change% = 涨跌额 / 昨收价 × 100%）小数点 4舍5入 保留2位
        double gain = double.parse((latestPrice - close).toStringAsFixed(4));
        double chg = 0.0;
        if (gain != 0) {
          chg = gain / close * 100;
        }
        double gainDisplay = double.parse(gain.toStringAsFixed(2));
        double chgDisplay = double.parse(chg.toStringAsFixed(2));

        // 市场
        final market = klineFutures[i].data?.data?.detail?.market ?? 'SZSE';

        // 金额单位
        String currency = 'CNY';
        if (["SZSE", "SSE"].contains(market)) {
          currency = 'CNY';
        } else if (market == "HKEX") {
          currency = 'HKD';
        } else if (market == "US") {
          currency = 'USD';
        }

        if (klineFutures[i].data?.data?.detail != null) {
          indexStocks.add(IndexStockInfo(
            stockInfo: StockInfoResponse(
              data: klineFutures[i].data!.data!.detail!.copyWith(
                    chg: chgDisplay,
                    gain: gainDisplay,
                    currency: currency,
                  ),
            ),
            klineData: klineFutures[i].data ?? StockKlineResponse(),
          ));
        }
      }
      emit(state.copyWith(status: DataStatus.success, indexStocks: indexStocks));
    } catch (e) {
      emit(state.copyWith(status: DataStatus.failed));
    }
  }

  Future<void> reloadTimeline() async {
    unsubscribeFromTimeline();
    await fetchStockData();
    Future.delayed(Duration(seconds: 1), () {
      subscribeToTimeline();
    });
  }

  void subscribeToTimeline() {
    for (var element in state.indexes) {
      webSocketService.send({
        'type': SocketEvents.market,
        'action': SocketActions.timeLine,
        'params': {
          'instrument': element.instrument,
          'period': 'day',
          'operate': 'subscribe',
        }
      });
    }
  }

  void unsubscribeFromTimeline() {
    for (var element in state.indexes) {
      webSocketService.send({
        'type': SocketEvents.market,
        'action': SocketActions.timeLine,
        'params': {
          'instrument': element.instrument,
          'period': 'day',
          'operate': 'unsubscribe',
        }
      });
    }
  }
}

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/features/market/domain/models/index_page/index_page_response_order.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/index_page/index_page_response_position.dart';
import '../../domain/repositories/market_repository.dart';

part 'index_page_state.dart';

@injectable
class IndexPageCubit extends Cubit<IndexPageState> {
  IndexPageCubit(this.marketRepo) : super(const IndexPageState());

  final MarketRepository marketRepo;

  void init({required bool isTransactionDetail}) async {
    emit(state.copyWith(indexPageFetchStatus: DataStatus.loading));
    if (isTransactionDetail) {
      _loadIndexOrder();
    } else {
      _loadIndexPosition();
    }
  }

  void _loadIndexPosition() async {
    await _fetchIndexPosition(
      pageNumber: 1,
      pageSize: 20,
      isLoadMore: false,
    );
  }

  void _loadIndexOrder() async {
    await _fetchIndexOrder(
      pageNumber: 1,
      pageSize: 20,
      isLoadMore: false,
    );
  }

  void loadMore({required bool isTransactionDetail}) async {
    if (isTransactionDetail) {
      _loadMoreIndexOrder();
    } else {
      _loadMoreIndexPosition();
    }
  }

  Future<void> _fetchIndexPosition({
    required int pageNumber,
    required int pageSize,
    required bool isLoadMore,
  }) async {
    if (isLoadMore) {
      emit(state.copyWith(indexPageLoadMoreStatus: DataStatus.loading));
    }
    try {
      final result = await marketRepo.getIndexPagePosition(
        pageNumber: pageNumber,
        pageSize: pageSize,
      );

      if (result.data != null) {
        if (isLoadMore) {
          final updatedRecords =
              List<IndexRecord>.from([...state.indexPagePositionResponse.records, ...result.data!.records]);
          emit(
            state.copyWith(
              indexPageResponse: result.data?.copyWith(records: updatedRecords),
              indexPageLoadMoreStatus: DataStatus.success,
            ),
          );
        } else {
          emit(
            state.copyWith(
              indexPageFetchStatus: DataStatus.success,
              indexPageResponse: result.data,
            ),
          );
        }
      } else {
        if (isLoadMore) {
          emit(state.copyWith(error: result.error, indexPageLoadMoreStatus: DataStatus.failed));
        } else {
          emit(
            state.copyWith(
              indexPageFetchStatus: DataStatus.failed,
              error: result.error,
            ),
          );
        }
      }
    } catch (e) {
      if (isLoadMore) {
        emit(state.copyWith(error: e.toString(), indexPageLoadMoreStatus: DataStatus.failed));
      } else {
        emit(
          state.copyWith(
            indexPageFetchStatus: DataStatus.failed,
            error: e.toString(),
          ),
        );
      }
    }
  }

  Future<void> _fetchIndexOrder({
    required int pageNumber,
    required int pageSize,
    required bool isLoadMore,
  }) async {
    if (isLoadMore) {
      emit(state.copyWith(indexPageLoadMoreStatus: DataStatus.loading));
    }
    try {
      final result = await marketRepo.getIndexPageOrder(
        pageNumber: pageNumber,
        pageSize: pageSize,
      );

      if (result.data != null) {
        if (isLoadMore) {
          final updatedRecords =
              List<IndexOrderRecord>.from([...state.indexPageOrderResponse.records, ...result.data!.records]);
          emit(
            state.copyWith(
              indexPageOrderResponse: result.data?.copyWith(records: updatedRecords),
              indexPageLoadMoreStatus: DataStatus.success,
            ),
          );
        } else {
          emit(
            state.copyWith(
              indexPageFetchStatus: DataStatus.success,
              indexPageOrderResponse: result.data,
            ),
          );
        }
      } else {
        if (isLoadMore) {
          emit(state.copyWith(error: result.error, indexPageLoadMoreStatus: DataStatus.failed));
        } else {
          emit(
            state.copyWith(
              indexPageFetchStatus: DataStatus.failed,
              error: result.error,
            ),
          );
        }
      }
    } catch (e) {
      if (isLoadMore) {
        emit(state.copyWith(error: e.toString(), indexPageLoadMoreStatus: DataStatus.failed));

      } else {
        emit(
          state.copyWith(
            indexPageFetchStatus: DataStatus.failed,
            error: e.toString(),
          ),
        );
      }
    }
  }

  void _loadMoreIndexPosition() async {
    if (!state.indexPagePositionResponse.hasNext) return;
    await _fetchIndexPosition(
      pageNumber: state.indexPagePositionResponse.current + 1,
      pageSize: 20,
      isLoadMore: true,
    );
  }

  void _loadMoreIndexOrder() async {
    if (!state.indexPageOrderResponse.hasNext) return;
    await _fetchIndexOrder(
      pageNumber: state.indexPageOrderResponse.current + 1,
      pageSize: 20,
      isLoadMore: true,
    );
  }
}

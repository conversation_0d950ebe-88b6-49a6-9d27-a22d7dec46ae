import 'package:equatable/equatable.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/search/market_search.dart';
import '../../domain/repositories/market_repository.dart';

part 'search_state.dart';

@injectable
class SearchCubit extends HydratedCubit<SearchState> {
  SearchCubit(this.marketRepo) : super(const SearchState());

  final MarketRepository marketRepo;

  void fetchSearchResults(String query, {String? market}) async {
    if (query.isEmpty) {
      resetSearch();
      return;
    }

    final prevResults = state.searchData;
    updateSearchValue(search: query);

    if (prevResults == null || prevResults.isEmpty) {
      emit(state.copyWith(searchFetchStatus: DataStatus.loading));
    }

    try {
      final result = await marketRepo.fetchSearchResults(query, market: market);

      if (state.searchValue == query) {
        emit(
          state.copyWith(
            searchFetchStatus: DataStatus.success,
            searchData: result.data?.data ?? [],
          ),
        );
      }
    } catch (e) {
      if (state.searchValue == query) {
        emit(
          state.copyWith(
            searchFetchStatus: DataStatus.failed,
            error: e.toString(),
          ),
        );
      }
      
    }
  }

  void addSearchItem({MarketSearchData? item}) {
    if (item == null) return;

    final currentHistory = state.searchDataHistory ?? [];

    final existingItemIndex =
        currentHistory.indexWhere((element) => element.symbol == item.symbol && element.market == item.market);

    List<MarketSearchData> updatedHistory;

    if (existingItemIndex >= 0) {
      updatedHistory = List.from(currentHistory)
        ..removeAt(existingItemIndex)
        ..insert(0, item);
    } else {
      updatedHistory = [item, ...currentHistory];

      if (updatedHistory.length > 20) {
        updatedHistory = updatedHistory.sublist(0, 20);
      }
    }

    emit(state.copyWith(searchDataHistory: updatedHistory));
  }

  @override
  SearchState fromJson(Map<String, dynamic> json) {
    final List<dynamic> historyJson = json['searchDataHistory'] ?? [];
    final searchDataHistory =
        historyJson.map((item) => MarketSearchData.fromJson(item as Map<String, dynamic>)).toList();
    return SearchState(searchDataHistory: searchDataHistory);
  }

  @override
  Map<String, dynamic> toJson(SearchState state) {
    return {
      'searchDataHistory': state.searchDataHistory?.map((e) => e.toJson()).toList(),
    };
  }

  void loadFromHistory() {
    if (state.searchDataHistory == null || state.searchDataHistory!.isEmpty) return;

    emit(state.copyWith(
      searchFetchStatus: DataStatus.success,
    ));
  }

  void clearAllSearchHistory() {
    emit(state.copyWith(
      searchDataHistory: [],
    ));
  }

  void clearSearchHistoryItem(MarketSearchData item) {
    if (state.searchDataHistory == null) return;
    final updatedHistory = state.searchDataHistory!
        .where((element) => element.symbol != item.symbol || element.market != item.market)
        .toList();

    emit(state.copyWith(
      searchDataHistory: updatedHistory,
    ));
  }

  void updateSearchValue({required String search}) {
    emit(state.copyWith(searchValue: search));
  }

  void setSearching() {
    if (state.searchFetchStatus != DataStatus.loading && state.searchValue != null) {
      emit(state.copyWith(
        searchFetchStatus: DataStatus.loading,
      ));
    }
  }

  void resetSearch() => emit(state.copyWith(
        resetSearchValue: true,
        searchData: null,
      ));
}

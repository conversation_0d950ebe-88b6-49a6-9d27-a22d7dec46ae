import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/contract.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/extension/future_list_extensions.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/view_models/market_category_state.dart';

import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/services/polling/polling_sevice_v2.dart';

import 'account_contract_detail_state.dart';

class AccountContractDetailCubit extends Cubit<AccountContractDetailState> {
  AccountContractDetailCubit({required ContractSummaryPageRecord data})
      : super(AccountContractDetailState(data: data)) {
    startPolling();
  }

  CancelToken? _positionCancelToken;
  CancelToken? _tradeCancelToken;
  CancelToken? _entrustCancelToken;

  Future<void> startPolling() async {
    getIt<PollingServiceV2>().startPolling(
      id: kGPAccountContractDetailPolling,
      onPoll: () async {
        fetchContractData();
        fetchOrderCurrentData();
        return true;
      },
      shouldStop: () => !getIt<UserCubit>().state.isLogin || isClosed,
    );
  }

  Future<void> fetchContractData() async {
    emit(state.copyWith(summaryDataState: DataStatus.loading));
    final res = await ContractApi.fetchContractSummaryDetail(contractId: state.data.id);
    if (res == null) {
      emit(state.copyWith(summaryDataState: DataStatus.failed));
      return;
    }
    emit(state.copyWith(data: res, summaryDataState: DataStatus.success));
  }

  /// 拉取订单列表：当前持仓、委托明细、成交明细
  /// Fetch market order list
  Future<void> fetchMarketOrderList({
    required OrderType type,
    required OrderListState orderListState,
    bool isLoadMore = false,
    bool isPolling = false,
  }) async {
    if (orderListState.status == DataStatus.loading) return;
    final newOrderListState = orderListState.copyWith(status: DataStatus.loading);

    updateOrderListState(type, newOrderListState);
    int page = 1;
    int pageSize = isPolling ? 100 : 20;
    if (isLoadMore) page = orderListState.page + 1;

    final bool isFetchPosition = type == OrderType.positions;

    CancelToken? cancelToken = _resetCancelToken(type);

    final result = isFetchPosition
        ? await ContractApi.getPositionList(
            page: page,
            pageSize: pageSize,
            contractId: state.data.id,
            cancelToken: cancelToken,
          )
        : await ContractApi.getOrderList(
            page: page,
            pageSize: pageSize,
            contractId: state.data.id,
            status: type == OrderType.trades ? 2 : 0,
            cancelToken: cancelToken);
    if (result == null) {
      final newOrderListState = orderListState.copyWith(status: DataStatus.failed, page: page, hasMoreData: true);
      updateOrderListState(type, newOrderListState);

      return;
    }
    final data = result.records;
    final records = isLoadMore && !isPolling ? orderListState.records.mergeDedupKeepLast(data, (e) => e.id) : data;
    final tmp = newOrderListState.copyWith(
      records: records,
      status: DataStatus.success,
      page: page,
      hasMoreData: data.length >= pageSize,
      isInitialLoad: false,
    );
    updateOrderListState(type, tmp);
  }

  void updateOrderListState(OrderType type, OrderListState orderListState) {
    final newViewModel = state.viewModel.updateDetail(type, orderListState);
    emit(state.copyWith(viewModel: newViewModel));
  }

  CancelToken _resetCancelToken(OrderType type) {
    switch (type) {
      case OrderType.positions:
        _positionCancelToken?.cancel();
        return _positionCancelToken = CancelToken();
      case OrderType.trades:
        _tradeCancelToken?.cancel();
        return _tradeCancelToken = CancelToken();
      case OrderType.order:
        _entrustCancelToken?.cancel();
        return _entrustCancelToken = CancelToken();
    }
  }

  void updateOrderViewIndex(int newIndex) {
    final newViewModel = state.viewModel.copyWith(selectedIndex: newIndex);
    emit(state.copyWith(viewModel: newViewModel));
  }

  /// 刷新现货账户当前市场类型下的订单列表数据
  /// Refreshes spot account order list data for current market type
  void fetchOrderCurrentData() async {
    for (final entry in state.viewModel.details.entries) {
      fetchMarketOrderList(
        type: entry.key,
        orderListState: entry.value,
      );
    }
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';

import 'package:url_launcher/url_launcher.dart';

import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/logic/sys_settings/sys_settings_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

enum FloatingPosition {
  topLeft,
  topRight,
  topCenter,
  centerLeft,
  centerRight,
  center,
  bottomLeft,
  bottomRight,
  bottomCenter,
  custom
}

class FloatingWidgetManager {
  static final FloatingWidgetManager _instance = FloatingWidgetManager._internal();

  factory FloatingWidgetManager() => _instance;

  FloatingWidgetManager._internal();

  // Store the current widget state directly instead of using a GlobalKey
  DraggableFloatWidgetState? _currentWidgetState;

  // Register the widget state when it's created
  void registerWidgetState(DraggableFloatWidgetState state) {
    _currentWidgetState = state;
  }

  // Unregister the widget state when it's disposed
  void unregisterWidgetState(DraggableFloatWidgetState state) {
    if (_currentWidgetState == state) {
      _currentWidgetState = null;
    }
  }

  void updatePosition(FloatingPosition position, {Offset? customPosition}) {
    _currentWidgetState?.updatePosition(position, customPosition: customPosition);
  }
}

class DraggableFloatWidgetConfig {
  final FloatingPosition initialPosition;
  final Offset? customPosition;
  final double marginTop;
  final double marginRight;
  final double marginBottom;
  final Duration animationDuration;

  const DraggableFloatWidgetConfig({
    this.initialPosition = FloatingPosition.bottomRight,
    this.customPosition,
    this.marginTop = 0,
    this.marginRight = 0,
    this.marginBottom = 0,
    this.animationDuration = const Duration(milliseconds: 300),
  }) : assert(
          initialPosition != FloatingPosition.custom || customPosition != null,
          'customPosition must be provided when using FloatingPosition.custom',
        );
}

class DraggableFloatWidget extends StatefulWidget {
  final DraggableFloatWidgetConfig config;
  final VoidCallback? onPositionChanged;

  const DraggableFloatWidget({
    super.key,
    required this.config,
    this.onPositionChanged,
  });

  @override
  State<DraggableFloatWidget> createState() => DraggableFloatWidgetState();
}

class DraggableFloatWidgetState extends State<DraggableFloatWidget> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late Animation<Offset> _animation;
  Offset? _position;
  static const double _buttonSize = 56.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.config.animationDuration,
      vsync: this,
    );

    // Register this state with the FloatingWidgetManager
    FloatingWidgetManager().registerWidgetState(this);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _position = _getInitialPosition();
      if (mounted) setState(() {});
    });
  }

  void updatePosition(FloatingPosition newPosition, {Offset? customPosition}) {
    if (!mounted) return;

    final targetPosition = _calculatePosition(newPosition, customPosition);
    _animateToPosition(targetPosition);
  }

  Offset _calculatePosition(FloatingPosition position, [Offset? customPos]) {
    final screenSize = MediaQuery.of(navigatorKey.currentContext!).size;
    final padding = 16.gw;

    switch (position) {
      case FloatingPosition.topLeft:
        return Offset(padding, widget.config.marginTop + padding);

      case FloatingPosition.topRight:
        return Offset(
            screenSize.width - _buttonSize - padding - widget.config.marginRight, widget.config.marginTop + padding);

      case FloatingPosition.topCenter:
        return Offset((screenSize.width - _buttonSize) / 2, widget.config.marginTop + padding);

      case FloatingPosition.centerLeft:
        return Offset(padding, (screenSize.height - _buttonSize) / 2);

      case FloatingPosition.centerRight:
        return Offset(screenSize.width - _buttonSize - padding - widget.config.marginRight,
            (screenSize.height - _buttonSize) / 2);

      case FloatingPosition.center:
        return Offset((screenSize.width - _buttonSize) / 2, (screenSize.height - _buttonSize) / 2);

      case FloatingPosition.bottomLeft:
        return Offset(padding, screenSize.height - _buttonSize - widget.config.marginBottom - padding);

      case FloatingPosition.bottomRight:
        return Offset(screenSize.width - _buttonSize - padding - widget.config.marginRight,
            screenSize.height - _buttonSize - widget.config.marginBottom - padding);

      case FloatingPosition.bottomCenter:
        return Offset((screenSize.width - _buttonSize) / 2,
            screenSize.height - _buttonSize - widget.config.marginBottom - padding);

      case FloatingPosition.custom:
        return customPos ?? _position ?? _getInitialPosition();
    }
  }

  Offset _getInitialPosition() {
    if (widget.config.initialPosition == FloatingPosition.custom && widget.config.customPosition != null) {
      return widget.config.customPosition!;
    }
    return _calculatePosition(widget.config.initialPosition);
  }

  void _animateToPosition(Offset targetPosition) {
    _animation = Tween(
      begin: _position,
      end: targetPosition,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutQuad,
    ));

    _animation.addListener(() {
      if (mounted) {
        setState(() => _position = _animation.value);
      }
    });

    _controller.forward(from: 0);
  }

  void _handleDrag(DragUpdateDetails details) {
    if (!mounted) return;

    setState(() {
      final screenSize = MediaQuery.of(context).size;
      final newPosition = _position! + details.delta;

      // Constrain x position
      final minX = 16.gw;
      final maxX = screenSize.width - _buttonSize - 16.gw - widget.config.marginRight;
      final x = newPosition.dx.clamp(minX, maxX);

      // Constrain y position
      final minY = widget.config.marginTop + 16.gh;
      final maxY = screenSize.height - _buttonSize - widget.config.marginBottom;
      final y = newPosition.dy.clamp(minY, maxY);

      _position = Offset(x, y);
      widget.onPositionChanged?.call();
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    final screenSize = MediaQuery.of(context).size;
    final targetX = _position!.dx < screenSize.width / 2
        ? 16.gw
        : screenSize.width - _buttonSize - 16.gw - widget.config.marginRight;

    _animateToPosition(Offset(targetX, _position!.dy));
  }

  @override
  void dispose() {
    // Unregister this state from the FloatingWidgetManager
    FloatingWidgetManager().unregisterWidgetState(this);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_position == null) return const SizedBox.shrink();

    return Positioned(
      left: _position?.dx,
      top: _position?.dy,
      child: GestureDetector(
        onPanUpdate: _handleDrag,
        onPanEnd: _handleDragEnd,
        child: FloatingActionButton(
          backgroundColor: context.theme.primaryColor,
          onPressed: () {
            // Verify authentication before opening the service URL
            context.verifyAuth(() {
              // Get the service URL from system settings
              final state = context.read<SysSettingsCubit>().state;

              // Use pattern matching with maybeWhen to handle different states
              state.maybeWhen(
                loaded: (sysSettings, _) {
                  final serviceUrl = sysSettings.service;
                  if (serviceUrl != null && serviceUrl.isNotEmpty) {
                    launchUrl(Uri.parse(serviceUrl), mode: LaunchMode.inAppBrowserView);
                  } else {
                    GPEasyLoading.showToast('something_went_wrong'.tr());
                  }
                },
                orElse: () {
                  // Fallback if system settings are not loaded
                  GPEasyLoading.showToast('something_went_wrong'.tr());
                },
              );
            });
          },
          child: const Icon(LucideIcons.message_circle, color: Colors.white),
        ),
      ),
    );
  }
}

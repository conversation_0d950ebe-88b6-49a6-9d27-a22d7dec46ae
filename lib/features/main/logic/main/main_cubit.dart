import 'dart:ui';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/shared/constants/web_socket_actions.dart';
import 'package:gp_stock_app/shared/constants/web_socket_types.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_scoket_interface.dart';
import 'package:injectable/injectable.dart';

part 'main_state.dart';

@singleton
class MainCubit extends Cubit<MainState> {
  MainCubit() : super(const MainState(selectedNavigationItem: NavigationItem.home));

  void selectedNavigationItem(NavigationItem item) {
    emit(state.copyWith(selectedNavigationItem: item));
    FTradeListRepository().checkAndClearCacheIfExpired();
  }

  void sendLocale(Locale locale) {
    getIt<WebSocketService>().send({
      'type': SocketEvents.clientInfo,
      'action': SocketActions.setClientLanguage,
      'params': {'language': '${locale.languageCode}-${locale.countryCode}'}
    });
  }

  void onChangeShowChatFloatWidget(bool show) => emit(state.copyWith(showChatFloatWidget: show));
}

import 'package:gp_stock_app/features/account/domain/models/calculate_config/calculate_config.dart';

import '../models/account_info/account_info_response.dart';
import '../models/account_summary/contract_summary_response.dart';
import '../models/order/order_response.dart';

abstract class AccountRepository {
  Future<AccountInfoResponse?> getAccountInfo();
  Future<ContractSummaryResponse?> getContractSummary({int page, int? settlementStatus});
  Future<ContractSummaryData?> getCurrentContractSummary(int contractId);
  Future<OrderResponse?> getOrderList({
    int page = 1,
    int? pageSize,
    int? status,
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType,
  });

  Future<OrderRecord?> getOrderById({
    required int id,
  });
  Future<OrderResponse?> getPositionList({
    int page,
    int? pageSize,
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType,
  });
  Future<OrderResponse?> getOrderHistoryTransaction({
    int page,
    int? contractId,
    String? commentAssetId,
    int? status,
  });
  Future<OrderResponse?> getOrderHistoryCommission({
    int page,
    int? contractId,
    String? commentAssetId,
  });
  Future<List<CalculateConfig>?> getCalculateConfig({
    required String market,
    required String direction,
    required String securityType,
    String? chargePackageId,
  });
}

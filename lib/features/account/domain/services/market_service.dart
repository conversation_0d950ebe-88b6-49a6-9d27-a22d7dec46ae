import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/company_info/company_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/dist_flow/dist_flow_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/market_status/market_status_response.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';

class MarketService {
  Future<ResponseModel<StockKlineResponse>> getKlineDetailList(String instrument, String period, String type) async {
    return await Http().request<StockKlineResponse>(
      '/market/$type',
      method: HttpMethod.get,
      queryParameters: {
        'period': period,
        'instrument': instrument,
      },
    );
  }

  Future<ResponseModel<MarketStatusResponse>> getMarketStatus({
    required String market,
    required String symbol,
    required String securityType,
  }) async {
    return await Http().request<MarketStatusResponse>(
      '/market/status',
      method: HttpMethod.get,
      queryParameters: {
        'market': market,
        'symbol': symbol,
        'securityType': securityType,
      },
    );
  }

  Future<ResponseModel<CompanyInfoResponse>> getCompanyInfo({
    required String market,
    required String symbol,
    required String securityType,
  }) async {
    return await Http().request<CompanyInfoResponse>(
      '/market/companyInfo',
      method: HttpMethod.get,
      queryParameters: {
        'market': market,
        'symbol': symbol,
        'securityType': securityType,
      },
    );
  }

  Future<ResponseModel<DistFlowResponse>> getDistFlow({
    required String market,
    required String symbol,
    required String securityType,
  }) async {
    return await Http().request<DistFlowResponse>(
      '/market/distFlow',
      method: HttpMethod.get,
      queryParameters: {
        'market': market,
        'symbol': symbol,
        'securityType': securityType,
      },
    );
  }
}

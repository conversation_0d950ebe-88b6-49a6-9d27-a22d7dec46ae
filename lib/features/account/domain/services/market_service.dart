import 'package:gp_stock_app/core/api/network/models/result.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/company_info/company_info_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/dist_flow/dist_flow_response.dart';
import 'package:gp_stock_app/features/account/domain/models/quotes/market_status/market_status_response.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';

class MarketService {
  Future<ResponseResult<StockKlineResponse>> getKlineDetailList(String instrument, String period, String type) async {
    try {
      final response = await Http().request<StockKlineResponse>(
        '/market/$type',
        method: HttpMethod.get,
        queryParameters: {
          'period': period,
          'instrument': instrument,
        },
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to fetch kline data');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  Future<ResponseResult<MarketStatusResponse>> getMarketStatus({
    required String market,
    required String symbol,
    required String securityType,
  }) async {
    try {
      final response = await Http().request<MarketStatusResponse>(
        '/market/status',
        method: HttpMethod.get,
        queryParameters: {
          'market': market,
          'symbol': symbol,
          'securityType': securityType,
        },
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to fetch market status');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  Future<ResponseResult<CompanyInfoResponse>> getCompanyInfo({
    required String market,
    required String symbol,
    required String securityType,
  }) async {
    try {
      final response = await Http().request<CompanyInfoResponse>(
        '/market/companyInfo',
        method: HttpMethod.get,
        queryParameters: {
          'market': market,
          'symbol': symbol,
          'securityType': securityType,
        },
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to fetch company info');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }

  Future<ResponseResult<DistFlowResponse>> getDistFlow({
    required String market,
    required String symbol,
    required String securityType,
  }) async {
    try {
      final response = await Http().request<DistFlowResponse>(
        '/market/distFlow',
        method: HttpMethod.get,
        queryParameters: {
          'market': market,
          'symbol': symbol,
          'securityType': securityType,
        },
      );

      if (response.isSuccess && response.data != null) {
        return ResponseResult(data: response.data);
      } else {
        return ResponseResult(error: response.msg ?? 'Failed to fetch dist flow');
      }
    } catch (e) {
      return ResponseResult(error: e.toString());
    }
  }
}

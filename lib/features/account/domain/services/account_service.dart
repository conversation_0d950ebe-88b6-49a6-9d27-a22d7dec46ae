import 'package:gp_stock_app/features/account/domain/models/calculate_config/calculate_config.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/services/http/http.dart';
import '../models/account_info/account_info_response.dart';
import '../models/account_summary/contract_summary_response.dart';
import '../models/order/order_response.dart';
import '../repository/account_repository.dart';

@Injectable(as: AccountRepository)
class AccountService implements AccountRepository {
  @override
  Future<AccountInfoResponse?> getAccountInfo() async {
    final response = await Http().request<AccountInfoResponse>(
      ApiEndpoints.getAccountInfo,
      method: HttpMethod.get,
    );
    return response.data;
  }

  @override
  Future<ContractSummaryData?> getCurrentContractSummary(int contractId) async {
    final response = await Http().request<ContractSummaryData>(
      '${ApiEndpoints.getContractSummary}/$contractId',
      method: HttpMethod.get,
    );
    return response.data;
  }

  @override
  Future<ContractSummaryResponse?> getContractSummary({int page = 1, int? settlementStatus}) async {
    final response = await Http().request<ContractSummaryResponse>(
      ApiEndpoints.getContractSummaryPage,
      method: HttpMethod.get,
      queryParameters: {
        'settlementStatus': settlementStatus,
        'pageNumber': page,
        'pageSize': 20,
      },
    );
    return response.data;
  }

  @override
  Future<OrderResponse?> getOrderList({
    int page = 1,
    int? pageSize = 20,
    int? status, // 订单委托状态： 0 委托中 1委托撤销 2.订单成交成功 3.合约到期自动撤销
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType, // 	数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
  }) async {
    /// https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%AE%A2%E5%8D%95%E7%9B%B8%E5%85%B3/pageUsingGET_11
    final response = await Http().request<OrderResponse>(
      ApiEndpoints.getOrderList,
      method: HttpMethod.get,
      queryParameters: {
        'pageNum': page,
        'pageSize': pageSize,
        if (dataType != null) 'dataType': dataType,
        if (contractId != null) 'contractId': contractId,
        if (status != null) 'status': status,
        if (symbol != null) 'symbol': symbol,
        if (market != null) 'market': market,
        if (securityType != null) 'securityType': securityType,
        if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
      },
    );
    return response.data;
  }

  @override
  Future<OrderRecord?> getOrderById({
    required int id,
  }) async {
    final response = await Http().request<OrderRecord>(
      ApiEndpoints.getOrderById,
      method: HttpMethod.get,
      queryParameters: {
        'id': id,
      },
    );
    return response.data;
  }

  @override
  Future<OrderResponse?> getPositionList({
    int page = 1,
    int? pageSize = 20,
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType, // 	数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
  }) async {
    final response = await Http().request<OrderResponse>(
      ApiEndpoints.getPositionList,
      method: HttpMethod.get,
      queryParameters: {
        'pageNumber': page,
        'pageSize': pageSize,
        if (dataType != null) 'dataType': dataType,
        if (contractId != null) 'contractId': contractId,
        if (symbol != null) 'symbol': symbol,
        if (market != null) 'market': market,
        if (securityType != null) 'securityType': securityType,
        if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
      },
    );
    return response.data;
  }

  /// 成交历史查询
  @override
  Future<OrderResponse?> getOrderHistoryTransaction({
    int page = 1,
    int? contractId,
    String? commentAssetId,
    int? status,
  }) async {
    return getOrderList(
      page: page,
      status: status,
      contractId: contractId,
      commentAssetId: commentAssetId,
    );
  }

  /// 佣金记录查询（只传 contractId/commentAssetId）
  @override
  Future<OrderResponse?> getOrderHistoryCommission({
    int page = 1,
    int? contractId,
    String? commentAssetId,
  }) async {
    return getOrderList(
      page: page,
      contractId: contractId,
      commentAssetId: commentAssetId,
    );
  }

  @override
  Future<List<CalculateConfig>?> getCalculateConfig({
    required String market,
    required String direction,
    required String securityType,
    String? chargePackageId,
  }) async {
    final response = await Http().request<List<CalculateConfig>>(
      ApiEndpoints.getCharge,
      method: HttpMethod.get,
      queryParameters: {
        'market': market,
        'direction': direction,
        'securityType': securityType,
        if (chargePackageId != null) 'chargePackageId': chargePackageId,
      },
    );
    return response.data;
  }
}

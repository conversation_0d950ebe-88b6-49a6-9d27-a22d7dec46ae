import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';

class CurrencyCalculatorKeyboard extends StatelessWidget {
  final TextEditingController controller;
  final Function(String)? onValueChanged;
  final Function(String)? onCalculate;
  final double? height;

  const CurrencyCalculatorKeyboard({
    super.key,
    required this.controller,
    this.onValueChanged,
    this.onCalculate,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 320.gh,
      padding: EdgeInsets.all(16.gw),
      color: const Color(0xFF1E3A5F),
      child: Row(
        children: [
          // Left side - numbers (3x4 grid)
          Expanded(
            flex: 3,
            child: Column(
              children: [
                _buildNumberRow(context, ['7', '8', '9']),
                _buildNumberRow(context, ['4', '5', '6']),
                _buildNumberRow(context, ['1', '2', '3']),
                _buildNumberRow(context, ['00', '0', '.']),
              ],
            ),
          ),
          // Middle column - operators
          SizedBox(
            width: 55.gw,
            child: Column(
              children: [
                _buildOperatorButton(context, '+'),
                _buildOperatorButton(context, '-'),
                _buildOperatorButton(context, '×'),
                _buildOperatorButton(context, '÷'),
              ],
            ),
          ),
          // Right column - special buttons and equals
          16.horizontalSpace,
          SizedBox(
            width: 60.gw,
            child: Column(
              children: [
                _buildSpecialButton(context, 'backspace'),
                _buildSpecialButton(context, 'C'),
                // Equal button spanning remaining space
                Expanded(
                  flex: 2,
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: 12.gh, horizontal: 4.gw),
                    child: Material(
                      color: const Color(0xFF2D6DB9),
                      borderRadius: BorderRadius.circular(8.gr),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8.gr),
                        onTap: () => _calculateResult(),
                        child: Center(
                          child: Text(
                            '=',
                            style: context.textTheme.primary.fs24.w700.copyWith(color: context.theme.cardColor),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNumberRow(BuildContext context, List<String> keys) {
    return Expanded(
      child: Row(
        children: keys.map((key) => _buildNumberButton(context, key)).toList(),
      ),
    );
  }

  Widget _buildNumberButton(BuildContext context, String key) {
    return Expanded(
      child: Material(
        color: const Color(0xFF1E3A5F),
        child: InkWell(
          borderRadius: BorderRadius.circular(50.gr),
          onTap: () => _handleKeyPress(key),
          child: Center(
            child: Text(
              key,
              style: context.textTheme.primary.fs24.w700.copyWith(color: context.theme.cardColor),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOperatorButton(BuildContext context, String operator) {
    return Container(
      width: 55.gw,
      height: 55.gh,
      margin: EdgeInsets.symmetric(vertical: 8.gh),
      child: Material(
        color: const Color(0xFF2D6DB9),
        borderRadius: BorderRadius.circular(50.gr),
        child: InkWell(
          borderRadius: BorderRadius.circular(50.gr),
          onTap: () => _handleKeyPress(operator),
          child: Center(
            child: Text(
              operator,
              style: context.textTheme.primary.fs24.w700.copyWith(color: context.theme.cardColor),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSpecialButton(BuildContext context, String type) {
    return Expanded(
      child: Container(
        width: 55.gw,
        height: 55.gh,
        margin: EdgeInsets.symmetric(vertical: 8.gh),
        child: Material(
          color: const Color(0xFF778899),
          borderRadius: BorderRadius.circular(50.gr),
          child: InkWell(
            borderRadius: BorderRadius.circular(50.gr),
            onTap: () {
              if (type == 'backspace') {
                _handleBackspace();
                HapticFeedback.lightImpact();
              } else if (type == 'C') {
                _updateText('0');
                HapticFeedback.lightImpact();
              }
            },
            child: Center(
              child: type == 'backspace'
                  ? Icon(Icons.backspace_outlined, color: context.theme.cardColor, size: 24.gw)
                  : Text(type, style: context.textTheme.primary.fs24.w700.copyWith(color: context.theme.cardColor)),
            ),
          ),
        ),
      ),
    );
  }

  // Handle backspace with cursor position awareness
  void _handleBackspace() {
    final text = controller.text;
    final selection = controller.selection;

    if (text.isEmpty) return;

    // If there's a selection, delete the selected text
    if (selection.start != selection.end) {
      final newText = text.replaceRange(selection.start, selection.end, '');
      final newCursorPosition = selection.start;

      controller.value = TextEditingValue(
        text: newText.isEmpty ? '0' : newText,
        selection: TextSelection.collapsed(offset: newCursorPosition),
      );

      // Notify of changes
      if (onValueChanged != null) {
        onValueChanged!(controller.text);
      }
      return;
    }

    // If there's no selection and cursor is not at the beginning
    if (selection.start > 0) {
      final newText = text.replaceRange(selection.start - 1, selection.start, '');
      final newCursorPosition = selection.start - 1;

      controller.value = TextEditingValue(
        text: newText.isEmpty ? '0' : newText,
        selection: TextSelection.collapsed(offset: newCursorPosition),
      );

      // Notify of changes
      if (onValueChanged != null) {
        onValueChanged!(controller.text);
      }
    }
  }

  // Calculate result of mathematical expression
  void _calculateResult() {
    try {
      final expression = controller.text.replaceAll('×', '*').replaceAll('÷', '/');
      print("KEYBOARD: Evaluating expression: $expression");

      final result = _evaluateExpression(expression);
      String formattedResult;

      // Format as integer if it's a whole number, otherwise use 4 decimal places
      if (result == result.toInt()) {
        formattedResult = result.toInt().toString();
      } else {
        formattedResult = result.toStringAsFixed(4);
      }

      print("KEYBOARD: Calculation result: $formattedResult");
      _updateText(formattedResult);

      // Notify of calculation completion
      if (onCalculate != null) {
        onCalculate!(formattedResult);
      }

      HapticFeedback.lightImpact();
    } catch (e) {
      print("KEYBOARD: Error evaluating expression: $e");
      HapticFeedback.heavyImpact();
    }
  }

  // Handle key press for numbers, decimals, and operators
  void _handleKeyPress(String key) {
    print("KEYBOARD: Key pressed: $key");

    final text = controller.text;
    final selection = controller.selection;

    // Prevent multiple decimal points in a single number
    if (key == '.') {
      final lastNumber = text.substring(0, selection.start).split(RegExp(r'[+\-*/]')).last;
      if (lastNumber.contains('.')) {
        return;
      }
    }

    // Handle '00' key
    if (key == '00') {
      if (text == '0' && selection.start == text.length) {
        return; // Prevent adding '00' to '0'
      }
    }

    // If text is '0' and key is not '.', replace the 0
    String newText;
    int newCursorPosition;
    if (text == '0' && key != '.') {
      newText = key;
      newCursorPosition = newText.length;
    }
    // If there's a selection, replace selected text with key
    else if (selection.start != selection.end) {
      newText = text.replaceRange(selection.start, selection.end, key);
      newCursorPosition = selection.start + key.length;
    }
    // No selection, insert at cursor position
    else {
      newText = text.substring(0, selection.start) + key + text.substring(selection.start);
      newCursorPosition = selection.start + key.length;
    }

    // Update controller with new text and cursor position
    controller.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newCursorPosition),
    );

    // Notify listeners of value change
    if (onValueChanged != null) {
      onValueChanged!(newText);
    }

    HapticFeedback.lightImpact();
  }

  // Update text and notify listeners
  void _updateText(String text) {
    // Update controller preserving cursor position
    final cursorPos = text.length;

    controller.value = TextEditingValue(
      text: text,
      selection: TextSelection.collapsed(offset: cursorPos),
    );

    // Notify listener of value change
    if (onValueChanged != null) {
      onValueChanged!(text);
    }
  }

  // Evaluate mathematical expression with proper decimal handling
  double _evaluateExpression(String expression) {
    try {
      // Replace operators for parsing
      final cleanExpression = expression.replaceAll('×', '*').replaceAll('÷', '/').trim();
      if (cleanExpression.isEmpty) return 0.0;

      // Tokenize the expression
      final tokens = _tokenizeExpression(cleanExpression);
      if (tokens.isEmpty) return 0.0;

      // Parse tokens into numbers and operators
      final numbers = <double>[];
      final operators = <String>[];

      for (var token in tokens) {
        if (token == '+' || token == '-' || token == '*' || token == '/') {
          operators.add(token);
        } else {
          numbers.add(double.parse(token));
        }
      }

      // Handle operator precedence (multiply and divide first)
      final tempNumbers = <double>[numbers[0]];
      final tempOperators = <String>[];

      for (int i = 0; i < operators.length; i++) {
        if (operators[i] == '*' || operators[i] == '/') {
          final left = tempNumbers.removeLast();
          final right = numbers[i + 1];
          final result = operators[i] == '*' ? left * right : left / right;
          tempNumbers.add(result);
        } else {
          tempNumbers.add(numbers[i + 1]);
          tempOperators.add(operators[i]);
        }
      }

      // Evaluate remaining additions and subtractions
      double result = tempNumbers[0];
      for (int i = 0; i < tempOperators.length; i++) {
        final right = tempNumbers[i + 1];
        result = tempOperators[i] == '+' ? result + right : result - right;
      }

      return result;
    } catch (e) {
      print("KEYBOARD: Error evaluating expression: $e");
      return 0.0;
    }
  }

  // Tokenize the expression into numbers and operators
  List<String> _tokenizeExpression(String expression) {
    final tokens = <String>[];
    String currentNumber = '';
    bool isNegative = false;

    for (int i = 0; i < expression.length; i++) {
      final char = expression[i];

      if (char == '+' || char == '*' || char == '/') {
        if (currentNumber.isNotEmpty) {
          tokens.add(isNegative ? '-$currentNumber' : currentNumber);
          isNegative = false;
          currentNumber = '';
        }
        tokens.add(char);
      } else if (char == '-') {
        if (currentNumber.isNotEmpty) {
          tokens.add(isNegative ? '-$currentNumber' : currentNumber);
          isNegative = false;
          currentNumber = '';
        }
        if (i == 0 ||
            expression[i - 1] == '+' ||
            expression[i - 1] == '-' ||
            expression[i - 1] == '*' ||
            expression[i - 1] == '/') {
          isNegative = true;
        } else {
          tokens.add(char);
        }
      } else if (char == '.' || (char.codeUnitAt(0) >= '0'.codeUnitAt(0) && char.codeUnitAt(0) <= '9'.codeUnitAt(0))) {
        currentNumber += char;
      }
    }

    if (currentNumber.isNotEmpty) {
      tokens.add(isNegative ? '-$currentNumber' : currentNumber);
    }

    return tokens;
  }
}

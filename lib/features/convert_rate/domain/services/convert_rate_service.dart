import 'package:gp_stock_app/core/models/apis/convert_rate.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import '../models/convert_rate_model.dart';

/// Provides methods for convert rate operations
class ConvertRateService {
  /// Static method to get convert rates
  static Future<ResponseModel<List<ConvertRate>>> getConvertRates() async {
    return await ConvertRateApi.getConvertRates();
  }
}
